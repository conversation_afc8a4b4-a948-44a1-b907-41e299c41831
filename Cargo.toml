[package]
name = "ckodex-cli"
version = "13.5.0"
edition = "2021"
description = "Constitutional AI Governance CLI"
authors = ["Ckodex Team <<EMAIL>>"]
license = "MIT OR Apache-2.0"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tonic = "0.10"
prost = "0.12"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
toml = "0.8"
config = "0.13"
clap = { version = "4.0", features = ["derive"] }
anyhow = "1.0"
tracing = "0.1"
reqwest = { version = "0.11", features = ["json"] }
chrono = "0.4"
sha2 = "0.10"
oci-distribution = "0.10"
tar = "0.4"
flate2 = "1.0"
dirs = "5.0"
shellexpand = "3.0"
url = "2.4"
handlebars = "4.5"
tera = "1.19"
# Database dependencies
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "postgres", "chrono", "uuid", "json"] }
sea-orm = { version = "0.12", features = ["sqlx-sqlite", "sqlx-postgres", "runtime-tokio-rustls", "macros", "with-chrono", "with-uuid"] }
sea-orm-migration = "0.12"
rust_decimal = { version = "1.30", features = ["serde-json"] }
# Additional utilities
indicatif = "0.17"
colored = "2.0"
uuid = { version = "1.0", features = ["v4"] }
tokio-stream = { version = "0.1", features = ["sync"] }
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
tempfile = "3.8"
async-stream = "0.3"
futures-util = "0.3"
async-trait = "0.1"

[build-dependencies]
tonic-build = "0.10"
