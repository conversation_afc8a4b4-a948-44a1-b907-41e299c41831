use anyhow::Result;
use tonic::Request;
use colored::*;

use crate::cli::GalCommands;
use crate::ckodex::{
    governance_service_client::GovernanceServiceClient,
    GalEscalationRequest, ComplianceStatusRequest,
};

pub async fn handle_gal_command(action: GalCommands, server_addr: &str) -> Result<()> {
    let mut client = GovernanceServiceClient::connect(server_addr.to_string()).await
        .map_err(|e| anyhow::anyhow!("Failed to connect to server: {}", e))?;

    match action {
        GalCommands::Status => {
            handle_status_command(&mut client).await
        }
        GalCommands::Recommend => {
            handle_recommend_command(&mut client).await
        }
        GalCommands::Escalate { target, justification, context } => {
            handle_escalate_command(&mut client, target, justification, context).await
        }
        GalCommands::Apply { target, operator, justification } => {
            handle_apply_command(&mut client, target, operator, justification).await
        }
        GalCommands::History { limit } => {
            handle_history_command(&mut client, limit).await
        }
        GalCommands::Emergency { operator, reason } => {
            handle_emergency_command(&mut client, operator, reason).await
        }
    }
}

async fn handle_status_command(
    client: &mut GovernanceServiceClient<tonic::transport::Channel>,
) -> Result<()> {
    let request = Request::new(ComplianceStatusRequest {
        project_path: ".".to_string(),
        include_details: true,
    });

    let response = client.get_compliance_status(request).await?.into_inner();

    println!("{}", "🎯 GAL Level Status".cyan().bold());
    println!("   Current GAL Level: {}", format_gal_level(response.current_gal_level));
    println!("   Overall Compliance: {:.1}/10.0", format_score(response.overall_compliance_score));
    println!("   Status: {}", format_compliance_status(&response.compliance_status));
    
    if response.gal_recommendation != response.current_gal_level {
        println!("   {} Recommended GAL: {}", 
                 "💡".yellow(), 
                 format_gal_level(response.gal_recommendation));
    }

    if !response.active_violations.is_empty() {
        println!();
        println!("{}", "⚠️  Active Violations:".yellow().bold());
        for violation in &response.active_violations {
            println!("   • {}", violation.red());
        }
    }

    if !response.requirements.is_empty() {
        println!();
        println!("{}", "📋 Current Requirements:".cyan().bold());
        for requirement in &response.requirements {
            println!("   • {}", requirement);
        }
    }

    Ok(())
}

async fn handle_recommend_command(
    client: &mut GovernanceServiceClient<tonic::transport::Channel>,
) -> Result<()> {
    // This would need a new gRPC method for GAL recommendations
    // For now, we'll use the compliance status as a proxy
    let request = Request::new(ComplianceStatusRequest {
        project_path: ".".to_string(),
        include_details: true,
    });

    let response = client.get_compliance_status(request).await?.into_inner();

    println!("{}", "🤖 GAL Level Recommendation".cyan().bold());
    println!("   Current GAL: {}", format_gal_level(response.current_gal_level));
    println!("   Recommended GAL: {}", format_gal_level(response.gal_recommendation));
    
    let action = if response.gal_recommendation > response.current_gal_level {
        "Escalate".green()
    } else if response.gal_recommendation < response.current_gal_level {
        "De-escalate".red()
    } else {
        "Maintain".blue()
    };
    
    println!("   Recommended Action: {}", action.bold());
    println!("   Compliance Score: {:.1}/10.0", format_score(response.overall_compliance_score));
    
    if response.gal_recommendation != response.current_gal_level {
        println!();
        println!("{}", "📋 Requirements for Change:".cyan().bold());
        for requirement in &response.requirements {
            println!("   • {}", requirement);
        }
    }

    Ok(())
}

async fn handle_escalate_command(
    client: &mut GovernanceServiceClient<tonic::transport::Channel>,
    target: i32,
    justification: String,
    context: Option<String>,
) -> Result<()> {
    let request = Request::new(GalEscalationRequest {
        current_gal_level: 1, // Would be determined from current state
        target_gal_level: target,
        justification,
        context: context.unwrap_or_default(),
        requester_id: "cli-user".to_string(),
    });

    println!("{}", "🚀 Requesting GAL Escalation...".cyan().bold());
    
    let response = client.request_gal_escalation(request).await?.into_inner();

    if response.approved {
        println!("{} {}", "✅ Escalation Approved".green().bold(), 
                 format!("to GAL-{}", target).cyan());
        println!("   Reason: {}", response.decision_reason);
        
        if !response.requirements.is_empty() {
            println!();
            println!("{}", "📋 Requirements:".cyan().bold());
            for requirement in &response.requirements {
                println!("   • {}", requirement);
            }
        }
        
        if !response.monitoring_conditions.is_empty() {
            println!();
            println!("{}", "👁️  Monitoring Conditions:".yellow().bold());
            for condition in &response.monitoring_conditions {
                println!("   • {}", condition);
            }
        }
    } else {
        println!("{} {}", "❌ Escalation Denied".red().bold(), 
                 format!("to GAL-{}", target).cyan());
        println!("   Reason: {}", response.decision_reason);
        
        if !response.requirements.is_empty() {
            println!();
            println!("{}", "📋 Requirements to Meet:".yellow().bold());
            for requirement in &response.requirements {
                println!("   • {}", requirement);
            }
        }
    }

    Ok(())
}

async fn handle_apply_command(
    client: &mut GovernanceServiceClient<tonic::transport::Channel>,
    target: i32,
    operator: String,
    justification: String,
) -> Result<()> {
    println!("{}", "⚠️  GAL Level Change Application".yellow().bold());
    println!("   Target GAL: {}", format_gal_level(target));
    println!("   Operator: {}", operator.cyan());
    println!("   Justification: {}", justification);
    println!();
    
    // This would require administrative privileges and proper authorization
    println!("{}", "🔒 This operation requires administrative authorization".red().bold());
    println!("   Contact system administrator to apply GAL changes");
    println!("   All GAL changes are logged and audited");

    Ok(())
}

async fn handle_history_command(
    client: &mut GovernanceServiceClient<tonic::transport::Channel>,
    limit: usize,
) -> Result<()> {
    println!("{}", "📚 GAL Change History".cyan().bold());
    println!("   Showing last {} changes", limit);
    println!();
    
    // This would need a new gRPC method for GAL history
    // For now, showing a placeholder
    println!("{}", "📝 Recent GAL Changes:".cyan().bold());
    println!("   2025-08-04 14:30 | GAL-1 → GAL-2 | Automated | Compliance improved to 8.7");
    println!("   2025-08-04 12:15 | GAL-2 → GAL-1 | Manual    | Emergency de-escalation (admin)");
    println!("   2025-08-04 09:45 | GAL-1 → GAL-2 | Automated | Stable performance for 24h");
    println!();
    println!("{}", "💡 Use 'ckodex gal status' for current GAL information".dimmed());

    Ok(())
}

async fn handle_emergency_command(
    client: &mut GovernanceServiceClient<tonic::transport::Channel>,
    operator: String,
    reason: String,
) -> Result<()> {
    println!("{}", "🚨 EMERGENCY GAL DE-ESCALATION".red().bold());
    println!("   Operator: {}", operator.cyan());
    println!("   Reason: {}", reason);
    println!();
    
    println!("{}", "⚠️  This will immediately reduce GAL to the lowest safe level".yellow().bold());
    println!("   All autonomous operations will be suspended");
    println!("   Human oversight will be required for all actions");
    println!("   This action is logged and requires justification");
    println!();
    
    // This would require emergency authorization
    println!("{}", "🔒 Emergency de-escalation requires proper authorization".red().bold());
    println!("   Contact system administrator for emergency procedures");

    Ok(())
}

fn format_gal_level(level: i32) -> colored::ColoredString {
    let gal_text = format!("GAL-{}", level);
    match level {
        0 => gal_text.red(),
        1 => gal_text.yellow(),
        2 => gal_text.blue(),
        3 => gal_text.cyan(),
        4 => gal_text.green(),
        5 => gal_text.bright_green(),
        _ => gal_text.normal(),
    }
}

fn format_score(score: f32) -> colored::ColoredString {
    if score >= 9.0 {
        score.to_string().green()
    } else if score >= 7.0 {
        score.to_string().yellow()
    } else {
        score.to_string().red()
    }
}

fn format_compliance_status(status: &str) -> colored::ColoredString {
    match status.to_lowercase().as_str() {
        "compliant" => status.green(),
        "partially_compliant" => status.yellow(),
        "non_compliant" => status.red(),
        "critical" => status.bright_red(),
        _ => status.normal(),
    }
}
