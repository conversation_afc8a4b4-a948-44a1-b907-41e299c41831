pub mod server;
pub mod artifact;
pub mod template;
pub mod config;
pub mod governance;
pub mod monitoring;
pub mod gal;

use anyhow::Result;
use tonic::transport::Channel;
use crate::ckodex::{
    artifact_service_client::ArtifactServiceClient,
    template_service_client::TemplateServiceClient,
    config_service_client::ConfigServiceClient,
    governance_service_client::GovernanceServiceClient,
};

/// Create gRPC clients for the given server address
pub struct GrpcClients {
    pub artifact: ArtifactServiceClient<Channel>,
    pub template: TemplateServiceClient<Channel>,
    pub config: ConfigServiceClient<Channel>,
    pub governance: GovernanceServiceClient<Channel>,
}

impl GrpcClients {
    pub async fn new(server_addr: &str) -> Result<Self> {
        let channel = Channel::from_shared(server_addr.to_string())?
            .connect()
            .await?;

        Ok(Self {
            artifact: ArtifactServiceClient::new(channel.clone()),
            template: TemplateServiceClient::new(channel.clone()),
            config: ConfigServiceClient::new(channel.clone()),
            governance: GovernanceServiceClient::new(channel),
        })
    }
}

/// Pretty print results in a formatted way
pub fn print_success(message: &str) {
    println!("✅ {}", message);
}

pub fn print_error(message: &str) {
    eprintln!("❌ {}", message);
}

pub fn print_warning(message: &str) {
    println!("⚠️  {}", message);
}

pub fn print_info(message: &str) {
    println!("ℹ️  {}", message);
}