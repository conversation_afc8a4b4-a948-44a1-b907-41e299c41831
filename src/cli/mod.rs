use clap::{Parser, Subcommand};
use anyhow::Result;
use std::path::PathBuf;

#[derive(Parser)]
#[command(name = "ckodex")]
#[command(about = "Constitutional AI Governance CLI")]
#[command(version = "13.5.0")]
pub struct Cli {
    #[command(subcommand)]
    pub command: Commands,

    /// Configuration file path
    #[arg(short, long, global = true)]
    pub config: Option<PathBuf>,

    /// Verbose output
    #[arg(short, long, global = true)]
    pub verbose: bool,

    /// Server address for gRPC operations
    #[arg(long, global = true, default_value = "http://[::1]:50052")]
    pub server: String,
}

#[derive(Subcommand)]
pub enum Commands {
    /// Start the gRPC server
    Server {
        /// Server bind address
        #[arg(short, long, default_value = "[::1]:50052")]
        addr: String,
    },
    /// Artifact operations
    Artifact {
        #[command(subcommand)]
        action: ArtifactCommands,
    },
    /// Template operations
    Template {
        #[command(subcommand)]
        action: TemplateCommands,
    },
    /// Configuration operations
    Config {
        #[command(subcommand)]
        action: ConfigCommands,
    },
    /// Constitutional governance operations
    Governance {
        #[command(subcommand)]
        action: GovernanceCommands,
    },
    /// Real-time constitutional monitoring operations
    Monitor {
        #[command(subcommand)]
        action: MonitorCommands,
    },
    /// GAL (Governed Autonomy Level) management operations
    Gal {
        #[command(subcommand)]
        action: GalCommands,
    },
}

#[derive(Subcommand)]
pub enum ArtifactCommands {
    /// Push artifact to registry
    Push {
        /// Artifact reference (e.g., localhost:5000/myapp:latest)
        reference: String,
        /// Source path
        #[arg(short, long)]
        source: PathBuf,
        /// Labels to attach
        #[arg(short, long)]
        labels: Vec<String>,
    },
    /// Pull artifact from registry
    Pull {
        /// Artifact reference
        reference: String,
        /// Output path
        #[arg(short, long)]
        output: Option<PathBuf>,
    },
    /// List artifacts in registry
    List,
    /// Get artifact metadata
    Info {
        /// Artifact reference
        reference: String,
    },
}

#[derive(Subcommand)]
pub enum TemplateCommands {
    /// Generate project template
    Generate {
        /// Template name
        template: String,
        /// Project name
        #[arg(short, long)]
        name: String,
        /// Industry vertical
        #[arg(short, long)]
        industry: Option<String>,
        /// Compliance frameworks
        #[arg(short, long)]
        compliance: Vec<String>,
        /// GAL level (0-5)
        #[arg(short, long, default_value = "1")]
        gal: i32,
        /// Output directory
        #[arg(short, long, default_value = ".")]
        output: PathBuf,
    },
    /// List available templates
    List,
    /// Customize existing template
    Customize {
        /// Template name
        template: String,
        /// Customization parameters
        #[arg(short, long)]
        params: Vec<String>,
    },
}

#[derive(Subcommand)]
pub enum ConfigCommands {
    /// Get configuration value
    Get {
        /// Configuration key
        key: String,
    },
    /// Set configuration value
    Set {
        /// Configuration key
        key: String,
        /// Configuration value
        value: String,
    },
    /// Validate configuration
    Validate {
        /// Configuration file to validate
        file: Option<PathBuf>,
    },
    /// Get GAL level recommendation
    GalRecommend {
        /// Current GAL level
        #[arg(short, long)]
        current: i32,
        /// Project context
        #[arg(short, long)]
        context: Option<String>,
    },
}

#[derive(Subcommand)]
pub enum GovernanceCommands {
    /// Perform constitutional audit
    Audit {
        /// Project path to audit
        #[arg(short, long, default_value = ".")]
        path: PathBuf,
        /// Focus on specific directives
        #[arg(short, long)]
        focus: Vec<String>,
        /// Output format (json, yaml, text)
        #[arg(short, long, default_value = "text")]
        output: String,
    },
    /// Request GAL escalation
    Escalate {
        /// Current GAL level
        current: i32,
        /// Target GAL level
        target: i32,
        /// Justification
        #[arg(short, long)]
        justification: String,
        /// Project context
        #[arg(short, long)]
        context: Option<String>,
    },
    /// Get compliance status
    Status {
        /// Project path
        #[arg(short, long, default_value = ".")]
        path: PathBuf,
    },
    /// Generate audit report
    Report {
        /// Project path
        #[arg(short, long, default_value = ".")]
        path: PathBuf,
        /// Output format (json, yaml, pdf)
        #[arg(short, long, default_value = "json")]
        format: String,
        /// Include details
        #[arg(short, long)]
        details: bool,
    },
}

#[derive(Subcommand)]
pub enum MonitorCommands {
    /// Get real-time constitutional compliance metrics
    Metrics {
        /// GAL level to filter for
        #[arg(short, long)]
        gal: Option<i32>,
        /// Include historical data
        #[arg(long)]
        history: bool,
        /// Stream updates continuously
        #[arg(short, long)]
        stream: bool,
    },
    /// Set compliance thresholds
    Threshold {
        /// GAL level
        #[arg(short, long)]
        gal: i32,
        /// Metric name
        #[arg(short, long)]
        metric: String,
        /// Warning threshold
        #[arg(short, long)]
        warning: f32,
        /// Critical threshold
        #[arg(short, long)]
        critical: f32,
    },
    /// Monitor violation alerts
    Alerts {
        /// Filter by severity
        #[arg(short, long)]
        severity: Vec<String>,
        /// Stream alerts continuously
        #[arg(long)]
        stream: bool,
    },
    /// Get compliance trends
    Trends {
        /// Start time (Unix timestamp)
        #[arg(short, long)]
        start: i64,
        /// End time (Unix timestamp)
        #[arg(short, long)]
        end: i64,
        /// Metrics to include
        #[arg(short, long)]
        metrics: Vec<String>,
    },
    /// Trigger manual compliance assessment
    Assess {
        /// Target path to assess
        #[arg(short, long, default_value = ".")]
        path: PathBuf,
        /// Focus on specific directives
        #[arg(short, long)]
        focus: Vec<String>,
        /// Force full scan
        #[arg(long)]
        force: bool,
    },
    /// Get system health status
    Health,
}

#[derive(Subcommand)]
pub enum GalCommands {
    /// Get current GAL level and status
    Status,
    /// Get GAL level recommendation based on current compliance
    Recommend,
    /// Request GAL level escalation
    Escalate {
        /// Target GAL level (0-5)
        #[arg(short, long)]
        target: i32,
        /// Justification for escalation
        #[arg(short, long)]
        justification: String,
        /// Additional context
        #[arg(short, long)]
        context: Option<String>,
    },
    /// Apply GAL level change (requires authorization)
    Apply {
        /// Target GAL level (0-5)
        #[arg(short, long)]
        target: i32,
        /// Operator name/ID
        #[arg(short, long)]
        operator: String,
        /// Justification for change
        #[arg(short, long)]
        justification: String,
    },
    /// Get GAL change history
    History {
        /// Number of recent changes to show
        #[arg(short, long, default_value = "10")]
        limit: usize,
    },
    /// Emergency GAL de-escalation
    Emergency {
        /// Operator name/ID
        #[arg(short, long)]
        operator: String,
        /// Emergency justification
        #[arg(short, long)]
        reason: String,
    },
}

pub mod handlers;

pub async fn run_cli() -> Result<()> {
    let cli = Cli::parse();

    // Initialize tracing based on verbosity
    let log_level = if cli.verbose { "debug" } else { "info" };
    tracing_subscriber::fmt()
        .with_env_filter(format!("ckodex={}", log_level))
        .init();

    match cli.command {
        Commands::Server { addr } => {
            handlers::server::run_server(addr).await
        }
        Commands::Artifact { action } => {
            handlers::artifact::handle_artifact_command(action, &cli.server).await
        }
        Commands::Template { action } => {
            handlers::template::handle_template_command(action, &cli.server).await
        }
        Commands::Config { action } => {
            handlers::config::handle_config_command(action, &cli.server).await
        }
        Commands::Governance { action } => {
            handlers::governance::handle_governance_command(action, &cli.server).await
        }
        Commands::Monitor { action } => {
            handlers::monitoring::handle_monitor_command(action, &cli.server).await
        }
        Commands::Gal { action } => {
            handlers::gal::handle_gal_command(action, &cli.server).await
        }
    }
}