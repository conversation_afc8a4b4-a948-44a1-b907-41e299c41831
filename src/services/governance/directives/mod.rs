use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc};

pub mod pd_001_human_oversight;
pub mod pd_002_transparency;
pub mod pd_003_data_privacy;
pub mod pd_004_bias_prevention;
pub mod pd_005_system_reliability;
pub mod pd_006_environmental;
pub mod pd_007_ethical_decisions;
pub mod pd_008_continuous_learning;
pub mod pd_009_accountability;
pub mod pd_010_constitutional;

/// Validation result for a single Primal Directive
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DirectiveValidationResult {
    pub directive_id: String,
    pub directive_name: String,
    pub compliance_score: f32, // 0.0 - 10.0
    pub status: ComplianceStatus,
    pub issues: Vec<String>,
    pub recommendations: Vec<String>,
    pub validation_timestamp: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Compliance status levels
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum ComplianceStatus {
    Compliant,      // Score >= 9.0
    Warning,        // Score >= 7.0 && < 9.0
    Violation,      // Score >= 5.0 && < 7.0
    Critical,       // Score < 5.0
}

impl ComplianceStatus {
    pub fn from_score(score: f32) -> Self {
        if score >= 9.0 {
            ComplianceStatus::Compliant
        } else if score >= 7.0 {
            ComplianceStatus::Warning
        } else if score >= 5.0 {
            ComplianceStatus::Violation
        } else {
            ComplianceStatus::Critical
        }
    }

    pub fn as_str(&self) -> &'static str {
        match self {
            ComplianceStatus::Compliant => "compliant",
            ComplianceStatus::Warning => "warning",
            ComplianceStatus::Violation => "violation",
            ComplianceStatus::Critical => "critical",
        }
    }
}

/// Context for directive validation
#[derive(Debug, Clone)]
pub struct ValidationContext {
    pub project_path: String,
    pub gal_level: i32,
    pub system_metrics: HashMap<String, f32>,
    pub historical_data: Vec<DirectiveValidationResult>,
    pub configuration: HashMap<String, serde_json::Value>,
}

/// Trait for implementing Primal Directive validators
#[async_trait::async_trait]
pub trait DirectiveValidator: Send + Sync {
    /// Get the directive ID (e.g., "PD-001")
    fn directive_id(&self) -> &str;
    
    /// Get the directive name
    fn directive_name(&self) -> &str;
    
    /// Get the directive description
    fn directive_description(&self) -> &str;
    
    /// Validate the directive against the current context
    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult>;
    
    /// Get the weight of this directive in overall compliance calculation
    fn weight(&self) -> f32 {
        1.0 // Default equal weight
    }
    
    /// Check if this directive is applicable for the given GAL level
    fn applicable_for_gal(&self, gal_level: i32) -> bool {
        gal_level >= 0 // Default: applicable for all GAL levels
    }
}

/// Main directive validator that orchestrates all Primal Directives
pub struct DirectiveValidatorEngine {
    validators: Vec<Box<dyn DirectiveValidator>>,
}

impl DirectiveValidatorEngine {
    pub fn new() -> Self {
        let mut validators: Vec<Box<dyn DirectiveValidator>> = Vec::new();
        
        // Register all Primal Directive validators
        validators.push(Box::new(pd_001_human_oversight::HumanOversightValidator::new()));
        validators.push(Box::new(pd_002_transparency::TransparencyValidator::new()));
        validators.push(Box::new(pd_003_data_privacy::DataPrivacyValidator::new()));
        validators.push(Box::new(pd_004_bias_prevention::BiasPreventionValidator::new()));
        validators.push(Box::new(pd_005_system_reliability::SystemReliabilityValidator::new()));
        validators.push(Box::new(pd_006_environmental::EnvironmentalValidator::new()));
        validators.push(Box::new(pd_007_ethical_decisions::EthicalDecisionValidator::new()));
        validators.push(Box::new(pd_008_continuous_learning::ContinuousLearningValidator::new()));
        validators.push(Box::new(pd_009_accountability::AccountabilityValidator::new()));
        validators.push(Box::new(pd_010_constitutional::ConstitutionalValidator::new()));
        
        Self { validators }
    }
    
    /// Validate all applicable directives for the given context
    pub async fn validate_all(&self, context: &ValidationContext) -> Result<Vec<DirectiveValidationResult>> {
        let mut results = Vec::new();
        
        for validator in &self.validators {
            if validator.applicable_for_gal(context.gal_level) {
                match validator.validate(context).await {
                    Ok(result) => results.push(result),
                    Err(e) => {
                        tracing::error!("Failed to validate {}: {}", validator.directive_id(), e);
                        // Create a critical failure result
                        results.push(DirectiveValidationResult {
                            directive_id: validator.directive_id().to_string(),
                            directive_name: validator.directive_name().to_string(),
                            compliance_score: 0.0,
                            status: ComplianceStatus::Critical,
                            issues: vec![format!("Validation failed: {}", e)],
                            recommendations: vec!["Review directive validation implementation".to_string()],
                            validation_timestamp: Utc::now(),
                            metadata: HashMap::new(),
                        });
                    }
                }
            }
        }
        
        Ok(results)
    }
    
    /// Calculate overall compliance score from directive results
    pub fn calculate_overall_score(&self, results: &[DirectiveValidationResult]) -> f32 {
        if results.is_empty() {
            return 0.0;
        }
        
        let total_weighted_score: f32 = results.iter()
            .zip(&self.validators)
            .map(|(result, validator)| result.compliance_score * validator.weight())
            .sum();
        
        let total_weight: f32 = self.validators.iter()
            .map(|v| v.weight())
            .sum();
        
        if total_weight > 0.0 {
            total_weighted_score / total_weight
        } else {
            0.0
        }
    }
    
    /// Get validator by directive ID
    pub fn get_validator(&self, directive_id: &str) -> Option<&dyn DirectiveValidator> {
        self.validators.iter()
            .find(|v| v.directive_id() == directive_id)
            .map(|v| v.as_ref())
    }
    
    /// Get all validator IDs
    pub fn get_all_directive_ids(&self) -> Vec<String> {
        self.validators.iter()
            .map(|v| v.directive_id().to_string())
            .collect()
    }
}

impl Default for DirectiveValidatorEngine {
    fn default() -> Self {
        Self::new()
    }
}

/// Helper function to create validation context
pub fn create_validation_context(
    project_path: String,
    gal_level: i32,
) -> ValidationContext {
    ValidationContext {
        project_path,
        gal_level,
        system_metrics: HashMap::new(),
        historical_data: Vec::new(),
        configuration: HashMap::new(),
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_compliance_status_from_score() {
        assert_eq!(ComplianceStatus::from_score(10.0), ComplianceStatus::Compliant);
        assert_eq!(ComplianceStatus::from_score(9.0), ComplianceStatus::Compliant);
        assert_eq!(ComplianceStatus::from_score(8.5), ComplianceStatus::Warning);
        assert_eq!(ComplianceStatus::from_score(7.0), ComplianceStatus::Warning);
        assert_eq!(ComplianceStatus::from_score(6.0), ComplianceStatus::Violation);
        assert_eq!(ComplianceStatus::from_score(5.0), ComplianceStatus::Violation);
        assert_eq!(ComplianceStatus::from_score(4.0), ComplianceStatus::Critical);
        assert_eq!(ComplianceStatus::from_score(0.0), ComplianceStatus::Critical);
    }

    #[tokio::test]
    async fn test_directive_validator_engine_creation() {
        let engine = DirectiveValidatorEngine::new();
        assert_eq!(engine.validators.len(), 10);
        
        let directive_ids = engine.get_all_directive_ids();
        assert!(directive_ids.contains(&"PD-001".to_string()));
        assert!(directive_ids.contains(&"PD-010".to_string()));
    }
}
