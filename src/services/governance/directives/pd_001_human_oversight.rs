use super::{DirectiveV<PERSON><PERSON><PERSON>, DirectiveValidationResult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;
use std::path::Path;

/// PD-001: Human Oversight Requirement
/// All autonomous operations must maintain human oversight capability
pub struct HumanOversightValidator;

impl HumanOversightValidator {
    pub fn new() -> Self {
        Self
    }

    /// Check for human-in-the-loop mechanisms in the codebase
    async fn check_human_oversight_mechanisms(&self, project_path: &str) -> Result<(f32, Vec<String>, Vec<String>)> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        let project_dir = Path::new(project_path);
        
        // Check for approval workflows
        let has_approval_workflows = self.check_approval_workflows(project_dir).await?;
        if !has_approval_workflows {
            score -= 2.0;
            issues.push("No approval workflows detected".to_string());
            recommendations.push("Implement approval workflows for critical operations".to_string());
        }

        // Check for human confirmation mechanisms
        let has_confirmation_mechanisms = self.check_confirmation_mechanisms(project_dir).await?;
        if !has_confirmation_mechanisms {
            score -= 2.0;
            issues.push("No human confirmation mechanisms found".to_string());
            recommendations.push("Add human confirmation for autonomous actions".to_string());
        }

        // Check for override capabilities
        let has_override_capabilities = self.check_override_capabilities(project_dir).await?;
        if !has_override_capabilities {
            score -= 2.0;
            issues.push("No human override capabilities detected".to_string());
            recommendations.push("Implement human override mechanisms".to_string());
        }

        // Check for monitoring dashboards
        let has_monitoring_dashboards = self.check_monitoring_dashboards(project_dir).await?;
        if !has_monitoring_dashboards {
            score -= 1.5;
            issues.push("No monitoring dashboards for human oversight".to_string());
            recommendations.push("Create monitoring dashboards for human operators".to_string());
        }

        // Check for escalation procedures
        let has_escalation_procedures = self.check_escalation_procedures(project_dir).await?;
        if !has_escalation_procedures {
            score -= 1.5;
            issues.push("No escalation procedures defined".to_string());
            recommendations.push("Define clear escalation procedures to human operators".to_string());
        }

        // Check for audit trails
        let has_audit_trails = self.check_audit_trails(project_dir).await?;
        if !has_audit_trails {
            score -= 1.0;
            issues.push("Insufficient audit trails for human review".to_string());
            recommendations.push("Enhance audit trails for human oversight".to_string());
        }

        Ok((score.max(0.0), issues, recommendations))
    }

    async fn check_approval_workflows(&self, project_dir: &Path) -> Result<bool> {
        // Check for workflow files, approval configurations, etc.
        let workflow_indicators = [
            ".github/workflows/",
            "workflows/",
            "approval.yml",
            "approval.yaml",
            "human-approval",
        ];

        for indicator in &workflow_indicators {
            if project_dir.join(indicator).exists() {
                return Ok(true);
            }
        }

        // Check for approval-related code patterns
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Ok(content) = std::fs::read_to_string(entry.path()) {
                    if content.contains("approval") || content.contains("human_approval") || content.contains("require_approval") {
                        return Ok(true);
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_confirmation_mechanisms(&self, project_dir: &Path) -> Result<bool> {
        // Check for confirmation-related patterns in code
        let confirmation_patterns = [
            "confirm",
            "confirmation",
            "user_confirmation",
            "human_confirm",
            "require_confirmation",
        ];

        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in &confirmation_patterns {
                                if content.contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_override_capabilities(&self, project_dir: &Path) -> Result<bool> {
        // Check for override mechanisms
        let override_patterns = [
            "override",
            "emergency_stop",
            "kill_switch",
            "human_override",
            "manual_override",
        ];

        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in &override_patterns {
                                if content.contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_monitoring_dashboards(&self, project_dir: &Path) -> Result<bool> {
        // Check for dashboard-related files and configurations
        let dashboard_indicators = [
            "dashboard",
            "monitoring",
            "grafana",
            "prometheus",
            "web-ui",
            "admin-panel",
        ];

        for indicator in &dashboard_indicators {
            if project_dir.join(indicator).exists() {
                return Ok(true);
            }
        }

        // Check for dashboard-related code
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Ok(content) = std::fs::read_to_string(entry.path()) {
                    if content.contains("dashboard") || content.contains("monitoring_ui") || content.contains("admin_panel") {
                        return Ok(true);
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_escalation_procedures(&self, project_dir: &Path) -> Result<bool> {
        // Check for escalation-related documentation and code
        let escalation_indicators = [
            "escalation",
            "escalate",
            "alert",
            "notification",
            "emergency",
        ];

        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Ok(content) = std::fs::read_to_string(entry.path()) {
                    for indicator in &escalation_indicators {
                        if content.contains(indicator) {
                            return Ok(true);
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_audit_trails(&self, project_dir: &Path) -> Result<bool> {
        // Check for audit trail mechanisms
        let audit_patterns = [
            "audit",
            "log",
            "trace",
            "tracking",
            "history",
        ];

        // Check for logging configurations
        let log_files = [
            "log4j.properties",
            "logback.xml",
            "logging.yml",
            "logging.yaml",
        ];

        for log_file in &log_files {
            if project_dir.join(log_file).exists() {
                return Ok(true);
            }
        }

        // Check for audit-related code
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Ok(content) = std::fs::read_to_string(entry.path()) {
                    for pattern in &audit_patterns {
                        if content.contains(pattern) {
                            return Ok(true);
                        }
                    }
                }
            }
        }

        Ok(false)
    }
}

#[async_trait::async_trait]
impl DirectiveValidator for HumanOversightValidator {
    fn directive_id(&self) -> &str {
        "PD-001"
    }

    fn directive_name(&self) -> &str {
        "Human Oversight Requirement"
    }

    fn directive_description(&self) -> &str {
        "All autonomous operations must maintain human oversight capability through approval workflows, confirmation mechanisms, override capabilities, monitoring dashboards, escalation procedures, and comprehensive audit trails."
    }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let (score, issues, recommendations) = self.check_human_oversight_mechanisms(&context.project_path).await?;
        
        let status = ComplianceStatus::from_score(score);
        
        let mut metadata = HashMap::new();
        metadata.insert("validation_method".to_string(), serde_json::Value::String("static_analysis".to_string()));
        metadata.insert("project_path".to_string(), serde_json::Value::String(context.project_path.clone()));
        metadata.insert("gal_level".to_string(), serde_json::Value::Number(serde_json::Number::from(context.gal_level)));

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score,
            status,
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata,
        })
    }

    fn weight(&self) -> f32 {
        1.2 // Higher weight for human oversight as it's critical for safety
    }

    fn applicable_for_gal(&self, gal_level: i32) -> bool {
        gal_level >= 1 // Not applicable for GAL-0 (basic operations)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::fs;

    #[tokio::test]
    async fn test_human_oversight_validator() {
        let validator = HumanOversightValidator::new();
        
        // Create a temporary directory for testing
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path().to_string_lossy().to_string();
        
        let context = ValidationContext {
            project_path: project_path.clone(),
            gal_level: 2,
            system_metrics: HashMap::new(),
            historical_data: Vec::new(),
            configuration: HashMap::new(),
        };

        let result = validator.validate(&context).await.unwrap();
        
        assert_eq!(result.directive_id, "PD-001");
        assert_eq!(result.directive_name, "Human Oversight Requirement");
        assert!(result.compliance_score >= 0.0 && result.compliance_score <= 10.0);
    }

    #[tokio::test]
    async fn test_approval_workflow_detection() {
        let validator = HumanOversightValidator::new();
        let temp_dir = TempDir::new().unwrap();
        
        // Create a workflow file
        let workflow_dir = temp_dir.path().join(".github/workflows");
        fs::create_dir_all(&workflow_dir).unwrap();
        fs::write(workflow_dir.join("approval.yml"), "name: Approval Workflow").unwrap();
        
        let has_workflows = validator.check_approval_workflows(temp_dir.path()).await.unwrap();
        assert!(has_workflows);
    }
}
