use super::{Directive<PERSON><PERSON><PERSON><PERSON>, DirectiveValidationR<PERSON>ult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;
use std::path::Path;

/// PD-002: Transparency and Explainability
/// All AI decisions must be explainable and auditable
pub struct TransparencyValidator;

impl TransparencyValidator {
    pub fn new() -> Self {
        Self
    }

    /// Check for transparency and explainability mechanisms
    async fn check_transparency_mechanisms(&self, project_path: &str) -> Result<(f32, Vec<String>, Vec<String>)> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        let project_dir = Path::new(project_path);
        
        // Check for decision logging
        let has_decision_logging = self.check_decision_logging(project_dir).await?;
        if !has_decision_logging {
            score -= 2.5;
            issues.push("No decision logging mechanisms detected".to_string());
            recommendations.push("Implement comprehensive decision logging".to_string());
        }

        // Check for explanation generation
        let has_explanation_generation = self.check_explanation_generation(project_dir).await?;
        if !has_explanation_generation {
            score -= 2.5;
            issues.push("No explanation generation capabilities found".to_string());
            recommendations.push("Add explanation generation for AI decisions".to_string());
        }

        // Check for model interpretability
        let has_model_interpretability = self.check_model_interpretability(project_dir).await?;
        if !has_model_interpretability {
            score -= 2.0;
            issues.push("No model interpretability mechanisms detected".to_string());
            recommendations.push("Implement model interpretability tools".to_string());
        }

        // Check for decision traceability
        let has_decision_traceability = self.check_decision_traceability(project_dir).await?;
        if !has_decision_traceability {
            score -= 1.5;
            issues.push("Insufficient decision traceability".to_string());
            recommendations.push("Enhance decision traceability mechanisms".to_string());
        }

        // Check for documentation quality
        let has_quality_documentation = self.check_documentation_quality(project_dir).await?;
        if !has_quality_documentation {
            score -= 1.0;
            issues.push("Insufficient documentation for transparency".to_string());
            recommendations.push("Improve documentation quality and coverage".to_string());
        }

        // Check for API transparency
        let has_api_transparency = self.check_api_transparency(project_dir).await?;
        if !has_api_transparency {
            score -= 0.5;
            issues.push("Limited API transparency".to_string());
            recommendations.push("Enhance API documentation and transparency".to_string());
        }

        Ok((score.max(0.0), issues, recommendations))
    }

    async fn check_decision_logging(&self, project_dir: &Path) -> Result<bool> {
        let logging_patterns = [
            "decision_log",
            "log_decision",
            "decision_audit",
            "choice_log",
            "reasoning_log",
        ];

        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in &logging_patterns {
                                if content.contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_explanation_generation(&self, project_dir: &Path) -> Result<bool> {
        let explanation_patterns = [
            "explain",
            "explanation",
            "reasoning",
            "rationale",
            "justify",
            "interpretation",
        ];

        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in &explanation_patterns {
                                if content.contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_model_interpretability(&self, project_dir: &Path) -> Result<bool> {
        let interpretability_indicators = [
            "shap",
            "lime",
            "interpretability",
            "explainable_ai",
            "feature_importance",
            "model_explanation",
        ];

        // Check for interpretability libraries in dependencies
        let dependency_files = ["Cargo.toml", "requirements.txt", "package.json", "pyproject.toml"];
        for dep_file in &dependency_files {
            if let Ok(content) = std::fs::read_to_string(project_dir.join(dep_file)) {
                for indicator in &interpretability_indicators {
                    if content.contains(indicator) {
                        return Ok(true);
                    }
                }
            }
        }

        // Check for interpretability code patterns
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for indicator in &interpretability_indicators {
                                if content.contains(indicator) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_decision_traceability(&self, project_dir: &Path) -> Result<bool> {
        let traceability_patterns = [
            "trace",
            "tracing",
            "span",
            "correlation_id",
            "request_id",
            "decision_path",
        ];

        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in &traceability_patterns {
                                if content.contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }

    async fn check_documentation_quality(&self, project_dir: &Path) -> Result<bool> {
        let doc_files = [
            "README.md",
            "DOCUMENTATION.md",
            "API.md",
            "ARCHITECTURE.md",
            "docs/",
        ];

        let mut doc_count = 0;
        let mut total_doc_size = 0;

        for doc_file in &doc_files {
            let doc_path = project_dir.join(doc_file);
            if doc_path.exists() {
                if doc_path.is_file() {
                    if let Ok(content) = std::fs::read_to_string(&doc_path) {
                        doc_count += 1;
                        total_doc_size += content.len();
                    }
                } else if doc_path.is_dir() {
                    // Count documentation files in docs directory
                    if let Ok(entries) = std::fs::read_dir(&doc_path) {
                        for entry in entries.flatten() {
                            if let Some(extension) = entry.path().extension() {
                                if extension == "md" || extension == "rst" || extension == "txt" {
                                    doc_count += 1;
                                    if let Ok(content) = std::fs::read_to_string(entry.path()) {
                                        total_doc_size += content.len();
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Consider documentation quality based on count and size
        Ok(doc_count >= 2 && total_doc_size >= 1000)
    }

    async fn check_api_transparency(&self, project_dir: &Path) -> Result<bool> {
        let api_doc_indicators = [
            "openapi",
            "swagger",
            "api_doc",
            "endpoint",
            "schema",
        ];

        // Check for API documentation files
        let api_files = [
            "openapi.yaml",
            "swagger.yaml",
            "api.yaml",
            "schema.json",
        ];

        for api_file in &api_files {
            if project_dir.join(api_file).exists() {
                return Ok(true);
            }
        }

        // Check for API documentation in code
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for indicator in &api_doc_indicators {
                                if content.contains(indicator) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }

        Ok(false)
    }
}

#[async_trait::async_trait]
impl DirectiveValidator for TransparencyValidator {
    fn directive_id(&self) -> &str {
        "PD-002"
    }

    fn directive_name(&self) -> &str {
        "Transparency and Explainability"
    }

    fn directive_description(&self) -> &str {
        "All AI decisions must be explainable and auditable through decision logging, explanation generation, model interpretability, decision traceability, quality documentation, and API transparency."
    }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let (score, issues, recommendations) = self.check_transparency_mechanisms(&context.project_path).await?;
        
        let status = ComplianceStatus::from_score(score);
        
        let mut metadata = HashMap::new();
        metadata.insert("validation_method".to_string(), serde_json::Value::String("static_analysis".to_string()));
        metadata.insert("project_path".to_string(), serde_json::Value::String(context.project_path.clone()));
        metadata.insert("gal_level".to_string(), serde_json::Value::Number(serde_json::Number::from(context.gal_level)));

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score,
            status,
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata,
        })
    }

    fn weight(&self) -> f32 {
        1.1 // Slightly higher weight for transparency as it's crucial for trust
    }

    fn applicable_for_gal(&self, gal_level: i32) -> bool {
        gal_level >= 0 // Applicable for all GAL levels
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;
    use std::fs;

    #[tokio::test]
    async fn test_transparency_validator() {
        let validator = TransparencyValidator::new();
        
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path().to_string_lossy().to_string();
        
        let context = ValidationContext {
            project_path: project_path.clone(),
            gal_level: 2,
            system_metrics: HashMap::new(),
            historical_data: Vec::new(),
            configuration: HashMap::new(),
        };

        let result = validator.validate(&context).await.unwrap();
        
        assert_eq!(result.directive_id, "PD-002");
        assert_eq!(result.directive_name, "Transparency and Explainability");
        assert!(result.compliance_score >= 0.0 && result.compliance_score <= 10.0);
    }

    #[tokio::test]
    async fn test_documentation_quality_detection() {
        let validator = TransparencyValidator::new();
        let temp_dir = TempDir::new().unwrap();
        
        // Create quality documentation
        fs::write(temp_dir.path().join("README.md"), "# Project Documentation\n\nThis is a comprehensive README with detailed information about the project architecture, usage, and implementation details.").unwrap();
        fs::write(temp_dir.path().join("API.md"), "# API Documentation\n\nDetailed API documentation with endpoints, parameters, and examples.").unwrap();
        
        let has_quality_docs = validator.check_documentation_quality(temp_dir.path()).await.unwrap();
        assert!(has_quality_docs);
    }
}
