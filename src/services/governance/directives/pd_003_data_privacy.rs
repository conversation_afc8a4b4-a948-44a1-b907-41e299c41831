use super::{Directive<PERSON><PERSON><PERSON><PERSON>, DirectiveValidationResult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;
use std::path::Path;

/// PD-003: Data Privacy and Security
/// All data handling must comply with privacy and security standards
pub struct DataPrivacyValidator;

impl DataPrivacyValidator {
    pub fn new() -> Self {
        Self
    }

    async fn check_data_privacy_mechanisms(&self, project_path: &str) -> Result<(f32, Vec<String>, Vec<String>)> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        let project_dir = Path::new(project_path);
        
        // Check for encryption mechanisms
        if !self.check_encryption_usage(project_dir).await? {
            score -= 3.0;
            issues.push("No encryption mechanisms detected".to_string());
            recommendations.push("Implement data encryption at rest and in transit".to_string());
        }

        // Check for access controls
        if !self.check_access_controls(project_dir).await? {
            score -= 2.5;
            issues.push("Insufficient access control mechanisms".to_string());
            recommendations.push("Implement robust access control systems".to_string());
        }

        // Check for data anonymization
        if !self.check_data_anonymization(project_dir).await? {
            score -= 2.0;
            issues.push("No data anonymization mechanisms found".to_string());
            recommendations.push("Add data anonymization and pseudonymization".to_string());
        }

        // Check for privacy compliance (GDPR, CCPA, etc.)
        if !self.check_privacy_compliance(project_dir).await? {
            score -= 1.5;
            issues.push("Limited privacy regulation compliance".to_string());
            recommendations.push("Ensure compliance with privacy regulations".to_string());
        }

        // Check for secure data storage
        if !self.check_secure_storage(project_dir).await? {
            score -= 1.0;
            issues.push("Insecure data storage practices detected".to_string());
            recommendations.push("Implement secure data storage practices".to_string());
        }

        Ok((score.max(0.0), issues, recommendations))
    }

    async fn check_encryption_usage(&self, project_dir: &Path) -> Result<bool> {
        let encryption_patterns = ["encrypt", "decrypt", "aes", "rsa", "tls", "ssl", "crypto"];
        self.check_patterns_in_code(project_dir, &encryption_patterns).await
    }

    async fn check_access_controls(&self, project_dir: &Path) -> Result<bool> {
        let access_patterns = ["auth", "authorization", "permission", "role", "rbac", "access_control"];
        self.check_patterns_in_code(project_dir, &access_patterns).await
    }

    async fn check_data_anonymization(&self, project_dir: &Path) -> Result<bool> {
        let anon_patterns = ["anonymize", "pseudonymize", "mask", "redact", "sanitize"];
        self.check_patterns_in_code(project_dir, &anon_patterns).await
    }

    async fn check_privacy_compliance(&self, project_dir: &Path) -> Result<bool> {
        let privacy_patterns = ["gdpr", "ccpa", "privacy", "consent", "data_subject"];
        self.check_patterns_in_code(project_dir, &privacy_patterns).await
    }

    async fn check_secure_storage(&self, project_dir: &Path) -> Result<bool> {
        let storage_patterns = ["secure_store", "vault", "keystore", "encrypted_db"];
        self.check_patterns_in_code(project_dir, &storage_patterns).await
    }

    async fn check_patterns_in_code(&self, project_dir: &Path, patterns: &[&str]) -> Result<bool> {
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in patterns {
                                if content.to_lowercase().contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }
        Ok(false)
    }
}

#[async_trait::async_trait]
impl DirectiveValidator for DataPrivacyValidator {
    fn directive_id(&self) -> &str { "PD-003" }
    fn directive_name(&self) -> &str { "Data Privacy and Security" }
    fn directive_description(&self) -> &str {
        "All data handling must comply with privacy and security standards through encryption, access controls, anonymization, privacy compliance, and secure storage."
    }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let (score, issues, recommendations) = self.check_data_privacy_mechanisms(&context.project_path).await?;
        
        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score,
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: HashMap::new(),
        })
    }

    fn weight(&self) -> f32 { 1.3 } // High weight for security
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 0 }
}
