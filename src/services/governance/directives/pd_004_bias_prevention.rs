use super::{DirectiveV<PERSON><PERSON><PERSON>, DirectiveValidationResult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;
use std::path::Path;

/// PD-004: Bias Prevention and Fairness
/// Systems must actively prevent and detect bias
pub struct BiasPreventionValidator;

impl BiasPreventionValidator {
    pub fn new() -> Self { Self }

    async fn check_bias_prevention_mechanisms(&self, project_path: &str) -> Result<(f32, Vec<String>, Vec<String>)> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        let project_dir = Path::new(project_path);
        
        if !self.check_bias_detection(project_dir).await? {
            score -= 2.5;
            issues.push("No bias detection mechanisms found".to_string());
            recommendations.push("Implement bias detection algorithms".to_string());
        }

        if !self.check_fairness_metrics(project_dir).await? {
            score -= 2.5;
            issues.push("No fairness metrics implementation".to_string());
            recommendations.push("Add fairness metrics and monitoring".to_string());
        }

        if !self.check_diverse_datasets(project_dir).await? {
            score -= 2.0;
            issues.push("Limited dataset diversity considerations".to_string());
            recommendations.push("Ensure diverse and representative datasets".to_string());
        }

        if !self.check_bias_mitigation(project_dir).await? {
            score -= 2.0;
            issues.push("No bias mitigation strategies detected".to_string());
            recommendations.push("Implement bias mitigation techniques".to_string());
        }

        if !self.check_algorithmic_auditing(project_dir).await? {
            score -= 1.0;
            issues.push("No algorithmic auditing mechanisms".to_string());
            recommendations.push("Add algorithmic auditing capabilities".to_string());
        }

        Ok((score.max(0.0), issues, recommendations))
    }

    async fn check_bias_detection(&self, project_dir: &Path) -> Result<bool> {
        let patterns = ["bias_detect", "fairness_check", "discrimination_test", "bias_audit"];
        self.check_patterns_in_code(project_dir, &patterns).await
    }

    async fn check_fairness_metrics(&self, project_dir: &Path) -> Result<bool> {
        let patterns = ["fairness_metric", "equalized_odds", "demographic_parity", "equal_opportunity"];
        self.check_patterns_in_code(project_dir, &patterns).await
    }

    async fn check_diverse_datasets(&self, project_dir: &Path) -> Result<bool> {
        let patterns = ["diverse_data", "representative_sample", "balanced_dataset", "demographic_balance"];
        self.check_patterns_in_code(project_dir, &patterns).await
    }

    async fn check_bias_mitigation(&self, project_dir: &Path) -> Result<bool> {
        let patterns = ["bias_mitigation", "debiasing", "fairness_constraint", "bias_correction"];
        self.check_patterns_in_code(project_dir, &patterns).await
    }

    async fn check_algorithmic_auditing(&self, project_dir: &Path) -> Result<bool> {
        let patterns = ["algorithm_audit", "model_audit", "fairness_audit", "bias_report"];
        self.check_patterns_in_code(project_dir, &patterns).await
    }

    async fn check_patterns_in_code(&self, project_dir: &Path, patterns: &[&str]) -> Result<bool> {
        if let Ok(entries) = std::fs::read_dir(project_dir) {
            for entry in entries.flatten() {
                if let Some(extension) = entry.path().extension() {
                    if extension == "rs" || extension == "py" || extension == "js" || extension == "ts" {
                        if let Ok(content) = std::fs::read_to_string(entry.path()) {
                            for pattern in patterns {
                                if content.to_lowercase().contains(pattern) {
                                    return Ok(true);
                                }
                            }
                        }
                    }
                }
            }
        }
        Ok(false)
    }
}

#[async_trait::async_trait]
impl DirectiveValidator for BiasPreventionValidator {
    fn directive_id(&self) -> &str { "PD-004" }
    fn directive_name(&self) -> &str { "Bias Prevention and Fairness" }
    fn directive_description(&self) -> &str {
        "Systems must actively prevent and detect bias through bias detection, fairness metrics, diverse datasets, bias mitigation, and algorithmic auditing."
    }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let (score, issues, recommendations) = self.check_bias_prevention_mechanisms(&context.project_path).await?;
        
        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score,
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: HashMap::new(),
        })
    }

    fn weight(&self) -> f32 { 1.1 }
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 1 }
}
