use super::{DirectiveValidator, DirectiveValidationResult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;

/// PD-005: System Reliability and Safety
pub struct SystemReliabilityValidator;

impl SystemReliabilityValidator {
    pub fn new() -> Self { Self }
}

#[async_trait::async_trait]
impl DirectiveValidator for SystemReliabilityValidator {
    fn directive_id(&self) -> &str { "PD-005" }
    fn directive_name(&self) -> &str { "System Reliability and Safety" }
    fn directive_description(&self) -> &str { "Systems must operate safely and reliably with high availability and low error rates." }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Check system metrics from context
        if let Some(error_rate) = context.system_metrics.get("error_rate") {
            if *error_rate > 5.0 {
                score -= 3.0;
                issues.push(format!("High error rate: {:.1}%", error_rate));
                recommendations.push("Reduce system error rate below 5%".to_string());
            }
        }

        if let Some(uptime) = context.system_metrics.get("uptime") {
            if *uptime < 99.0 {
                score -= 2.0;
                issues.push(format!("Low uptime: {:.1}%", uptime));
                recommendations.push("Improve system uptime to >99%".to_string());
            }
        }

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score.max(0.0),
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: HashMap::new(),
        })
    }

    fn weight(&self) -> f32 { 1.2 }
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 0 }
}
