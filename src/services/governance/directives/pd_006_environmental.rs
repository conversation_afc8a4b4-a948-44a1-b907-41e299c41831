use super::{DirectiveValida<PERSON>, DirectiveValidationR<PERSON>ult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;

/// PD-006: Environmental Responsibility
pub struct EnvironmentalValidator;

impl EnvironmentalValidator {
    pub fn new() -> Self { Self }
}

#[async_trait::async_trait]
impl DirectiveValidator for EnvironmentalValidator {
    fn directive_id(&self) -> &str { "PD-006" }
    fn directive_name(&self) -> &str { "Environmental Responsibility" }
    fn directive_description(&self) -> &str { "Operations must minimize environmental impact through efficient resource usage." }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Check resource usage metrics
        if let Some(cpu_usage) = context.system_metrics.get("cpu_usage") {
            if *cpu_usage > 80.0 {
                score -= 2.0;
                issues.push(format!("High CPU usage: {:.1}%", cpu_usage));
                recommendations.push("Optimize CPU usage for environmental efficiency".to_string());
            }
        }

        if let Some(memory_usage) = context.system_metrics.get("memory_usage") {
            if *memory_usage > 85.0 {
                score -= 1.5;
                issues.push(format!("High memory usage: {:.1}%", memory_usage));
                recommendations.push("Optimize memory usage".to_string());
            }
        }

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score.max(0.0),
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: HashMap::new(),
        })
    }

    fn weight(&self) -> f32 { 0.8 }
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 1 }
}
