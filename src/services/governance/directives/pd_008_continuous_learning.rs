use super::{DirectiveValidator, DirectiveValidationResult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;

/// PD-008: Continuous Learning and Improvement
pub struct ContinuousLearningValidator;

impl ContinuousLearningValidator {
    pub fn new() -> Self { Self }
}

#[async_trait::async_trait]
impl DirectiveValidator for ContinuousLearningValidator {
    fn directive_id(&self) -> &str { "PD-008" }
    fn directive_name(&self) -> &str { "Continuous Learning and Improvement" }
    fn directive_description(&self) -> &str { "Systems must continuously learn and improve performance over time." }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let mut score = 8.5; // Default good score
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Check for learning indicators in historical data
        if context.historical_data.len() >= 2 {
            let recent_scores: Vec<f32> = context.historical_data
                .iter()
                .filter(|d| d.directive_id == "PD-008")
                .map(|d| d.compliance_score)
                .collect();
            
            if recent_scores.len() >= 2 {
                let trend = recent_scores.last().unwrap() - recent_scores.first().unwrap();
                if trend < 0.0 {
                    score -= 1.5;
                    issues.push("Declining performance trend detected".to_string());
                    recommendations.push("Investigate and address performance decline".to_string());
                }
            }
        } else {
            score -= 0.5;
            issues.push("Insufficient historical data for learning assessment".to_string());
            recommendations.push("Collect more performance data over time".to_string());
        }

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score.max(0.0),
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: HashMap::new(),
        })
    }

    fn weight(&self) -> f32 { 0.9 }
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 2 }
}
