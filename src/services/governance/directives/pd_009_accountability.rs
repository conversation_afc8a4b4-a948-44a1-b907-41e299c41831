use super::{Directive<PERSON><PERSON><PERSON><PERSON>, DirectiveValidationR<PERSON>ult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;

/// PD-009: Accountability and Auditability
pub struct AccountabilityValidator;

impl AccountabilityValidator {
    pub fn new() -> Self { Self }
}

#[async_trait::async_trait]
impl DirectiveValidator for AccountabilityValidator {
    fn directive_id(&self) -> &str { "PD-009" }
    fn directive_name(&self) -> &str { "Accountability and Auditability" }
    fn directive_description(&self) -> &str { "All actions must be accountable and auditable with comprehensive trails." }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let mut score = 9.5; // High default score for accountability
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // Check if audit trails are being maintained
        if context.historical_data.is_empty() {
            score -= 2.0;
            issues.push("No audit trail data available".to_string());
            recommendations.push("Implement comprehensive audit trail logging".to_string());
        }

        // Check for accountability mechanisms based on GAL level
        match context.gal_level {
            0..=1 => {
                // Basic accountability requirements
                if !context.configuration.contains_key("audit_enabled") {
                    score -= 1.0;
                    issues.push("Basic audit logging not configured".to_string());
                    recommendations.push("Enable basic audit logging".to_string());
                }
            },
            2..=3 => {
                // Enhanced accountability requirements
                if !context.configuration.contains_key("detailed_audit") {
                    score -= 1.5;
                    issues.push("Detailed audit logging not configured".to_string());
                    recommendations.push("Enable detailed audit logging".to_string());
                }
            },
            4..=5 => {
                // Maximum accountability requirements
                if !context.configuration.contains_key("comprehensive_audit") {
                    score -= 2.0;
                    issues.push("Comprehensive audit logging not configured".to_string());
                    recommendations.push("Enable comprehensive audit logging".to_string());
                }
            },
            _ => {}
        }

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score.max(0.0),
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: HashMap::new(),
        })
    }

    fn weight(&self) -> f32 { 1.1 }
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 0 }
}
