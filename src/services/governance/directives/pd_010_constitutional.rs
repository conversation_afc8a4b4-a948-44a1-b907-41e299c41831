use super::{DirectiveV<PERSON><PERSON><PERSON>, DirectiveValidationR<PERSON>ult, ValidationContext, ComplianceStatus};
use anyhow::Result;
use chrono::Utc;
use std::collections::HashMap;

/// PD-010: Constitutional Adherence
pub struct ConstitutionalValidator;

impl ConstitutionalValidator {
    pub fn new() -> Self { Self }
}

#[async_trait::async_trait]
impl DirectiveValidator for ConstitutionalValidator {
    fn directive_id(&self) -> &str { "PD-010" }
    fn directive_name(&self) -> &str { "Constitutional Adherence" }
    fn directive_description(&self) -> &str { "All operations must adhere to the constitutional framework and other directives." }

    async fn validate(&self, context: &ValidationContext) -> Result<DirectiveValidationResult> {
        let mut score = 10.0;
        let mut issues = Vec::new();
        let mut recommendations = Vec::new();

        // This directive validates adherence to the overall constitutional framework
        // It checks compliance with other directives and the system's constitutional integrity
        
        // Check if other directives are being violated
        let mut violation_count = 0;
        let mut critical_violations = 0;
        
        for historical_result in &context.historical_data {
            if historical_result.directive_id != "PD-010" {
                match historical_result.status {
                    ComplianceStatus::Critical => {
                        critical_violations += 1;
                        violation_count += 1;
                    },
                    ComplianceStatus::Violation => {
                        violation_count += 1;
                    },
                    _ => {}
                }
            }
        }

        // Penalize based on violations of other directives
        if critical_violations > 0 {
            score -= 3.0;
            issues.push(format!("{} critical directive violations detected", critical_violations));
            recommendations.push("Address all critical directive violations immediately".to_string());
        }

        if violation_count > critical_violations {
            let regular_violations = violation_count - critical_violations;
            score -= (regular_violations as f32) * 0.5;
            issues.push(format!("{} directive violations detected", regular_violations));
            recommendations.push("Address all directive violations".to_string());
        }

        // Check constitutional framework integrity
        if context.gal_level > 3 && !context.configuration.contains_key("constitutional_framework") {
            score -= 1.0;
            issues.push("Constitutional framework not explicitly configured for high GAL level".to_string());
            recommendations.push("Configure explicit constitutional framework".to_string());
        }

        // Ensure minimum compliance threshold
        if context.historical_data.len() >= 9 { // At least 9 other directives
            let avg_compliance: f32 = context.historical_data
                .iter()
                .filter(|d| d.directive_id != "PD-010")
                .map(|d| d.compliance_score)
                .sum::<f32>() / (context.historical_data.len() - 1) as f32;
            
            if avg_compliance < 7.0 {
                score -= 2.0;
                issues.push(format!("Overall directive compliance too low: {:.1}", avg_compliance));
                recommendations.push("Improve overall constitutional compliance".to_string());
            }
        }

        Ok(DirectiveValidationResult {
            directive_id: self.directive_id().to_string(),
            directive_name: self.directive_name().to_string(),
            compliance_score: score.max(0.0),
            status: ComplianceStatus::from_score(score),
            issues,
            recommendations,
            validation_timestamp: Utc::now(),
            metadata: {
                let mut metadata = HashMap::new();
                metadata.insert("violation_count".to_string(), serde_json::Value::Number(serde_json::Number::from(violation_count)));
                metadata.insert("critical_violations".to_string(), serde_json::Value::Number(serde_json::Number::from(critical_violations)));
                metadata
            },
        })
    }

    fn weight(&self) -> f32 { 1.5 } // Highest weight as it validates overall constitutional adherence
    fn applicable_for_gal(&self, gal_level: i32) -> bool { gal_level >= 0 }
}
