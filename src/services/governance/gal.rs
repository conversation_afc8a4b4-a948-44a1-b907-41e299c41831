use anyhow::Result;
use serde::{Serialize, Deserialize};
use std::collections::HashMap;
use chrono::{DateTime, Utc, Duration};
use crate::ckodex::GalEscalationResponse;

use super::directives::{DirectiveValidationResult, ComplianceStatus};

/// Governance Autonomy Level (GAL) system
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, PartialOrd, Ord, Serialize, Deserialize)]
pub enum GalLevel {
    /// GAL-0: Manual Review Mode - Every operation requires explicit approval
    Manual = 0,
    /// GAL-1: Assisted Coordination - 2-3 file operations, basic automation
    Assisted = 1,
    /// GAL-2: Supervised Autonomy - Multi-module operations, advanced automation
    Supervised = 2,
    /// GAL-3: Conditional Orchestration - Full service generation, system-wide changes
    Conditional = 3,
    /// GAL-4: High Autonomy - Complete system design, self-governing operations
    HighAutonomy = 4,
    /// GAL-5: Full Autonomy - Unrestricted operations, system evolution
    FullAutonomy = 5,
}

impl GalLevel {
    pub fn from_i32(value: i32) -> Option<Self> {
        match value {
            0 => Some(Self::Manual),
            1 => Some(Self::Assisted),
            2 => Some(Self::Supervised),
            3 => Some(Self::Conditional),
            4 => Some(Self::HighAutonomy),
            5 => Some(Self::FullAutonomy),
            _ => None,
        }
    }

    pub fn to_i32(self) -> i32 {
        self as i32
    }

    pub fn description(&self) -> &'static str {
        match self {
            Self::Manual => "Manual Review Mode - Single-file operations only",
            Self::Assisted => "Assisted Coordination - 2-3 file coordination allowed",
            Self::Supervised => "Supervised Autonomy - Multi-module implementations",
            Self::Conditional => "Conditional Orchestration - Full service generation",
            Self::HighAutonomy => "High Autonomy - Complete system design",
            Self::FullAutonomy => "Full Autonomy - System evolution capabilities",
        }
    }

    pub fn max_concurrent_operations(&self) -> u32 {
        match self {
            Self::Manual => 1,
            Self::Assisted => 3,
            Self::Supervised => 10,
            Self::Conditional => 25,
            Self::HighAutonomy => 50,
            Self::FullAutonomy => u32::MAX,
        }
    }

    pub fn requires_human_approval(&self) -> bool {
        matches!(self, Self::Manual | Self::Assisted)
    }

    pub fn escalation_requirements(&self) -> Vec<String> {
        match self {
            Self::Manual => vec![
                "Demonstrate basic competency".to_string(),
                "Complete security training".to_string(),
            ],
            Self::Assisted => vec![
                "Show understanding of multi-file operations".to_string(),
                "Pass constitutional compliance test".to_string(),
            ],
            Self::Supervised => vec![
                "Demonstrate advanced technical skills".to_string(),
                "Complete architectural review".to_string(),
                "Maintain >90% compliance score".to_string(),
            ],
            Self::Conditional => vec![
                "Pass comprehensive system design assessment".to_string(),
                "Demonstrate enterprise-level competency".to_string(),
                "Maintain >95% compliance score".to_string(),
                "Complete advanced security clearance".to_string(),
            ],
            Self::HighAutonomy => vec![
                "Demonstrate system evolution capabilities".to_string(),
                "Pass expert-level assessment".to_string(),
                "Maintain >98% compliance score".to_string(),
                "Complete constitutional governance training".to_string(),
            ],
            Self::FullAutonomy => vec![
                "Reserved for system administrators".to_string(),
                "Requires board-level approval".to_string(),
                "Maintain perfect compliance record".to_string(),
            ],
        }
    }
}

#[derive(Debug, Default)]
pub struct GalValidator {
    // Validation state and configuration
}

impl GalValidator {
    pub fn new() -> Self {
        Self::default()
    }

    /// Evaluate a GAL escalation request
    pub async fn evaluate_escalation(
        &self,
        current: GalLevel,
        target: GalLevel,
        justification: &str,
        context: &str,
    ) -> Result<GalEscalationResponse> {
        tracing::debug!(
            "Evaluating GAL escalation: {} -> {} ({})",
            current.to_i32(),
            target.to_i32(),
            justification
        );

        // Basic validation
        if target <= current {
            return Ok(GalEscalationResponse {
                approved: false,
                decision_reason: "Target GAL level must be higher than current level".to_string(),
                requirements: vec![],
                monitoring_conditions: vec![],
            });
        }

        if target.to_i32() - current.to_i32() > 2 {
            return Ok(GalEscalationResponse {
                approved: false,
                decision_reason: "Cannot escalate more than 2 GAL levels at once".to_string(),
                requirements: vec!["Request incremental escalation".to_string()],
                monitoring_conditions: vec![],
            });
        }

        // Evaluate justification quality
        let justification_score = self.evaluate_justification(justification, current, target);
        let context_score = self.evaluate_context(context, target);
        
        // Calculate approval probability
        let approval_score = (justification_score + context_score) / 2.0;
        let approved = approval_score >= 0.7;

        let decision_reason = if approved {
            format!("Escalation approved based on strong justification (score: {:.2})", approval_score)
        } else {
            format!("Escalation denied due to insufficient justification (score: {:.2})", approval_score)
        };

        let requirements = target.escalation_requirements();
        let monitoring_conditions = self.get_monitoring_conditions(target);

        Ok(GalEscalationResponse {
            approved,
            decision_reason,
            requirements,
            monitoring_conditions,
        })
    }

    /// Evaluate the quality of escalation justification
    fn evaluate_justification(&self, justification: &str, current: GalLevel, target: GalLevel) -> f64 {
        let mut score = 0.0;

        // Length and detail check
        if justification.len() > 50 {
            score += 0.2;
        }
        if justification.len() > 200 {
            score += 0.2;
        }

        // Keyword analysis for technical competency
        let technical_keywords = [
            "architecture", "system", "performance", "security", "compliance",
            "governance", "constitutional", "scalability", "integration"
        ];
        
        let technical_score = technical_keywords.iter()
            .filter(|&&keyword| justification.to_lowercase().contains(keyword))
            .count() as f64 / technical_keywords.len() as f64;
        
        score += technical_score * 0.3;

        // Business justification
        let business_keywords = [
            "efficiency", "productivity", "automation", "workflow", "process",
            "requirement", "deadline", "project", "deliverable"
        ];
        
        let business_score = business_keywords.iter()
            .filter(|&&keyword| justification.to_lowercase().contains(keyword))
            .count() as f64 / business_keywords.len() as f64;
        
        score += business_score * 0.3;

        // Adjust for GAL level requirements
        match (current, target) {
            (GalLevel::Manual, GalLevel::Assisted) => score += 0.1, // Easy escalation
            (GalLevel::Supervised, GalLevel::Conditional) => {
                if justification.contains("enterprise") || justification.contains("production") {
                    score += 0.2;
                }
            }
            (GalLevel::Conditional, GalLevel::HighAutonomy) => {
                if justification.len() < 300 {
                    score -= 0.3; // Require detailed justification
                }
            }
            _ => {}
        }

        score.min(1.0).max(0.0)
    }

    /// Evaluate the project context for GAL escalation
    fn evaluate_context(&self, context: &str, target: GalLevel) -> f64 {
        let score = 0.5; // Baseline score

        if context.is_empty() {
            return 0.2; // Penalize empty context
        }

        // Context complexity appropriate for target GAL
        let complexity_indicators = [
            "microservices", "distributed", "enterprise", "production",
            "high-availability", "scalable", "fault-tolerant"
        ];

        let complexity_score = complexity_indicators.iter()
            .filter(|&&indicator| context.to_lowercase().contains(indicator))
            .count() as f64 / complexity_indicators.len() as f64;

        match target {
            GalLevel::Manual | GalLevel::Assisted => score,
            GalLevel::Supervised => score + (complexity_score * 0.2),
            GalLevel::Conditional => score + (complexity_score * 0.3),
            GalLevel::HighAutonomy => {
                if complexity_score < 0.3 {
                    score - 0.3 // High autonomy requires complex context
                } else {
                    score + (complexity_score * 0.5)
                }
            }
            GalLevel::FullAutonomy => {
                if complexity_score < 0.5 {
                    score - 0.5 // Full autonomy requires very complex context
                } else {
                    score + (complexity_score * 0.5)
                }
            }
        }
    }

    /// Get monitoring conditions for a GAL level
    fn get_monitoring_conditions(&self, gal: GalLevel) -> Vec<String> {
        match gal {
            GalLevel::Manual => vec![
                "Human approval required for all operations".to_string(),
            ],
            GalLevel::Assisted => vec![
                "Weekly compliance review".to_string(),
                "Operation logging enabled".to_string(),
            ],
            GalLevel::Supervised => vec![
                "Daily automated compliance checks".to_string(),
                "Real-time performance monitoring".to_string(),
                "Weekly human review".to_string(),
            ],
            GalLevel::Conditional => vec![
                "Continuous compliance monitoring".to_string(),
                "Automated rollback on violations".to_string(),
                "Daily performance reports".to_string(),
                "Bi-weekly governance review".to_string(),
            ],
            GalLevel::HighAutonomy => vec![
                "Real-time constitutional monitoring".to_string(),
                "Automatic violation prevention".to_string(),
                "Continuous audit trail".to_string(),
                "Weekly governance board review".to_string(),
            ],
            GalLevel::FullAutonomy => vec![
                "Maximum security monitoring".to_string(),
                "Real-time board notifications".to_string(),
                "Immutable audit logs".to_string(),
                "Emergency protocol ready".to_string(),
            ],
        }
    }
}

/// GAL assessment context for automated management
#[derive(Debug, Clone)]
pub struct GalAssessmentContext {
    pub current_gal: GalLevel,
    pub compliance_score: f32,
    pub compliance_history: Vec<(DateTime<Utc>, f32)>,
    pub directive_results: Vec<DirectiveValidationResult>,
    pub violation_count: u32,
    pub critical_violations: u32,
    pub system_metrics: HashMap<String, f32>,
    pub last_gal_change: Option<DateTime<Utc>>,
    pub human_overrides: Vec<GalOverride>,
}

/// Human override record
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct GalOverride {
    pub timestamp: DateTime<Utc>,
    pub from_gal: GalLevel,
    pub to_gal: GalLevel,
    pub operator: String,
    pub justification: String,
    pub override_type: OverrideType,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum OverrideType {
    Emergency,
    Maintenance,
    Testing,
    Administrative,
}

/// GAL recommendation result
#[derive(Debug, Clone)]
pub struct GalRecommendation {
    pub current_gal: GalLevel,
    pub recommended_gal: GalLevel,
    pub action: GalAction,
    pub confidence: f32,
    pub reasoning: String,
    pub requirements: Vec<String>,
    pub monitoring_conditions: Vec<String>,
    pub estimated_timeline: Option<Duration>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum GalAction {
    Maintain,
    Escalate,
    DeEscalate,
    EmergencyDeEscalate,
}

/// Risk assessment levels
#[derive(Debug, Clone, PartialEq)]
pub enum RiskLevel {
    Low,
    Medium,
    High,
    Critical,
}

impl RiskLevel {
    pub fn from_score(score: f32) -> Self {
        if score >= 0.8 {
            RiskLevel::Critical
        } else if score >= 0.6 {
            RiskLevel::High
        } else if score >= 0.4 {
            RiskLevel::Medium
        } else {
            RiskLevel::Low
        }
    }
}