use anyhow::Result;
use chrono::{DateTime, Utc, Duration};
use std::collections::HashMap;
use tokio::sync::RwLock;
use std::sync::Arc;

use super::gal::{
    GalLevel, GalAssessmentContext, GalRecommendation, GalAction, GalOverride, 
    OverrideType, RiskLevel
};
use super::directives::{DirectiveValidationResult, ComplianceStatus};

/// Automated GAL Level Management Engine
pub struct GalManager {
    current_gal: Arc<RwLock<GalLevel>>,
    gal_history: Arc<RwLock<Vec<GalChangeRecord>>>,
    risk_thresholds: RiskThresholds,
    stability_requirements: HashMap<GalLevel, Duration>,
}

/// GAL change record for audit trail
#[derive(Debug, Clone)]
pub struct GalChangeRecord {
    pub timestamp: DateTime<Utc>,
    pub from_gal: GalLevel,
    pub to_gal: GalLevel,
    pub trigger: GalChangeTrigger,
    pub compliance_score: f32,
    pub risk_level: RiskLevel,
    pub justification: String,
    pub operator: Option<String>, // None for automated changes
}

#[derive(Debug, Clone)]
pub enum GalChangeTrigger {
    Automated,
    Manual,
    Emergency,
    Violation,
    Compliance,
}

/// Risk assessment thresholds
#[derive(Debug, Clone)]
pub struct RiskThresholds {
    pub escalation_min_compliance: f32,
    pub de_escalation_max_compliance: f32,
    pub emergency_de_escalation_threshold: f32,
    pub critical_violation_threshold: u32,
    pub stability_buffer_hours: i64,
}

impl Default for RiskThresholds {
    fn default() -> Self {
        Self {
            escalation_min_compliance: 8.5,
            de_escalation_max_compliance: 7.0,
            emergency_de_escalation_threshold: 5.0,
            critical_violation_threshold: 1,
            stability_buffer_hours: 24,
        }
    }
}

impl GalManager {
    pub fn new(initial_gal: GalLevel) -> Self {
        let mut stability_requirements = HashMap::new();
        stability_requirements.insert(GalLevel::Manual, Duration::hours(1));
        stability_requirements.insert(GalLevel::Assisted, Duration::hours(24));
        stability_requirements.insert(GalLevel::Supervised, Duration::hours(48));
        stability_requirements.insert(GalLevel::Conditional, Duration::hours(72));
        stability_requirements.insert(GalLevel::HighAutonomy, Duration::hours(168)); // 1 week
        stability_requirements.insert(GalLevel::FullAutonomy, Duration::hours(720)); // 1 month

        Self {
            current_gal: Arc::new(RwLock::new(initial_gal)),
            gal_history: Arc::new(RwLock::new(Vec::new())),
            risk_thresholds: RiskThresholds::default(),
            stability_requirements,
        }
    }

    /// Get current GAL level
    pub async fn get_current_gal(&self) -> GalLevel {
        *self.current_gal.read().await
    }

    /// Assess and recommend GAL level changes
    pub async fn assess_gal_level(&self, context: &GalAssessmentContext) -> Result<GalRecommendation> {
        let current_gal = context.current_gal;
        let compliance_score = context.compliance_score;
        
        // Calculate risk level
        let risk_level = self.calculate_risk_level(context).await;
        
        // Determine recommended action
        let (action, recommended_gal, reasoning) = self.determine_gal_action(context, &risk_level).await?;
        
        // Calculate confidence based on data quality and consistency
        let confidence = self.calculate_confidence(context, &action).await;
        
        // Get requirements and monitoring conditions
        let requirements = self.get_action_requirements(&action, current_gal, recommended_gal);
        let monitoring_conditions = self.get_monitoring_conditions(recommended_gal);
        
        // Estimate timeline for change
        let estimated_timeline = self.estimate_timeline(&action, current_gal, recommended_gal);

        Ok(GalRecommendation {
            current_gal,
            recommended_gal,
            action,
            confidence,
            reasoning,
            requirements,
            monitoring_conditions,
            estimated_timeline,
        })
    }

    /// Calculate risk level based on multiple factors
    async fn calculate_risk_level(&self, context: &GalAssessmentContext) -> RiskLevel {
        let mut risk_score = 0.0;

        // Compliance score risk
        if context.compliance_score < 7.0 {
            risk_score += 0.3;
        } else if context.compliance_score < 8.0 {
            risk_score += 0.1;
        }

        // Critical violations risk
        if context.critical_violations > 0 {
            risk_score += 0.4;
        }

        // Regular violations risk
        if context.violation_count > 3 {
            risk_score += 0.2;
        } else if context.violation_count > 1 {
            risk_score += 0.1;
        }

        // System performance risk
        if let Some(error_rate) = context.system_metrics.get("error_rate") {
            if *error_rate > 5.0 {
                risk_score += 0.2;
            }
        }

        if let Some(uptime) = context.system_metrics.get("uptime") {
            if *uptime < 99.0 {
                risk_score += 0.1;
            }
        }

        // Compliance trend risk
        if context.compliance_history.len() >= 2 {
            let recent_scores: Vec<f32> = context.compliance_history
                .iter()
                .rev()
                .take(5)
                .map(|(_, score)| *score)
                .collect();
            
            if recent_scores.len() >= 2 {
                let trend = recent_scores[0] - recent_scores[recent_scores.len() - 1];
                if trend < -1.0 {
                    risk_score += 0.2; // Declining trend
                }
            }
        }

        RiskLevel::from_score(risk_score)
    }

    /// Determine the recommended GAL action
    async fn determine_gal_action(&self, context: &GalAssessmentContext, risk_level: &RiskLevel) -> Result<(GalAction, GalLevel, String)> {
        let current_gal = context.current_gal;
        let compliance_score = context.compliance_score;

        // Emergency de-escalation conditions
        if compliance_score < self.risk_thresholds.emergency_de_escalation_threshold 
            || context.critical_violations >= self.risk_thresholds.critical_violation_threshold
            || matches!(risk_level, RiskLevel::Critical) {
            
            let target_gal = match current_gal {
                GalLevel::FullAutonomy => GalLevel::HighAutonomy,
                GalLevel::HighAutonomy => GalLevel::Conditional,
                GalLevel::Conditional => GalLevel::Supervised,
                GalLevel::Supervised => GalLevel::Assisted,
                GalLevel::Assisted => GalLevel::Manual,
                GalLevel::Manual => GalLevel::Manual, // Can't go lower
            };

            return Ok((
                GalAction::EmergencyDeEscalate,
                target_gal,
                format!("Emergency de-escalation due to critical compliance issues (score: {:.1}, violations: {})", 
                       compliance_score, context.critical_violations)
            ));
        }

        // Regular de-escalation conditions
        if compliance_score < self.risk_thresholds.de_escalation_max_compliance 
            || context.violation_count > 2
            || matches!(risk_level, RiskLevel::High) {
            
            let target_gal = match current_gal {
                GalLevel::FullAutonomy => GalLevel::HighAutonomy,
                GalLevel::HighAutonomy => GalLevel::Conditional,
                GalLevel::Conditional => GalLevel::Supervised,
                GalLevel::Supervised => GalLevel::Assisted,
                GalLevel::Assisted => GalLevel::Manual,
                GalLevel::Manual => GalLevel::Manual,
            };

            return Ok((
                GalAction::DeEscalate,
                target_gal,
                format!("De-escalation recommended due to compliance issues (score: {:.1}, risk: {:?})", 
                       compliance_score, risk_level)
            ));
        }

        // Escalation conditions
        if compliance_score >= self.risk_thresholds.escalation_min_compliance 
            && context.violation_count == 0
            && matches!(risk_level, RiskLevel::Low | RiskLevel::Medium)
            && self.check_stability_requirement(context).await {
            
            let target_gal = match current_gal {
                GalLevel::Manual => GalLevel::Assisted,
                GalLevel::Assisted => GalLevel::Supervised,
                GalLevel::Supervised => GalLevel::Conditional,
                GalLevel::Conditional => GalLevel::HighAutonomy,
                GalLevel::HighAutonomy => GalLevel::FullAutonomy,
                GalLevel::FullAutonomy => GalLevel::FullAutonomy, // Already at max
            };

            if target_gal != current_gal {
                return Ok((
                    GalAction::Escalate,
                    target_gal,
                    format!("Escalation recommended due to excellent compliance (score: {:.1}, stable performance)", 
                           compliance_score)
                ));
            }
        }

        // Maintain current level
        Ok((
            GalAction::Maintain,
            current_gal,
            format!("Maintaining current GAL level (score: {:.1}, risk: {:?})", 
                   compliance_score, risk_level)
        ))
    }

    /// Check if stability requirements are met for escalation
    async fn check_stability_requirement(&self, context: &GalAssessmentContext) -> bool {
        if let Some(last_change) = context.last_gal_change {
            let required_duration = self.stability_requirements
                .get(&context.current_gal)
                .copied()
                .unwrap_or(Duration::hours(24));
            
            let elapsed = Utc::now() - last_change;
            elapsed >= required_duration
        } else {
            true // No previous change, assume stable
        }
    }

    /// Calculate confidence in the recommendation
    async fn calculate_confidence(&self, context: &GalAssessmentContext, action: &GalAction) -> f32 {
        let mut confidence = 0.5; // Base confidence

        // Data quality factors
        if context.compliance_history.len() >= 5 {
            confidence += 0.2; // Good historical data
        }

        if context.directive_results.len() >= 8 {
            confidence += 0.1; // Comprehensive directive coverage
        }

        // Consistency factors
        if context.compliance_history.len() >= 3 {
            let recent_scores: Vec<f32> = context.compliance_history
                .iter()
                .rev()
                .take(3)
                .map(|(_, score)| *score)
                .collect();
            
            let variance = self.calculate_variance(&recent_scores);
            if variance < 0.5 {
                confidence += 0.2; // Consistent performance
            }
        }

        // Action-specific confidence adjustments
        match action {
            GalAction::EmergencyDeEscalate => confidence += 0.3, // High confidence in safety actions
            GalAction::DeEscalate => confidence += 0.1,
            GalAction::Maintain => confidence += 0.1,
            GalAction::Escalate => {
                // More cautious about escalation
                if context.compliance_score > 9.0 {
                    confidence += 0.1;
                } else {
                    confidence -= 0.1;
                }
            }
        }

        confidence.min(1.0).max(0.0)
    }

    /// Calculate variance of a set of scores
    fn calculate_variance(&self, scores: &[f32]) -> f32 {
        if scores.len() < 2 {
            return 0.0;
        }

        let mean = scores.iter().sum::<f32>() / scores.len() as f32;
        let variance = scores.iter()
            .map(|score| (score - mean).powi(2))
            .sum::<f32>() / scores.len() as f32;
        
        variance.sqrt()
    }

    /// Get requirements for a GAL action
    fn get_action_requirements(&self, action: &GalAction, current: GalLevel, target: GalLevel) -> Vec<String> {
        match action {
            GalAction::Maintain => vec!["Continue current monitoring and compliance practices".to_string()],
            GalAction::Escalate => target.escalation_requirements(),
            GalAction::DeEscalate => vec![
                "Address compliance issues before re-escalation".to_string(),
                "Implement additional monitoring".to_string(),
                "Review and improve processes".to_string(),
            ],
            GalAction::EmergencyDeEscalate => vec![
                "Immediate compliance issue resolution required".to_string(),
                "Enhanced human oversight implemented".to_string(),
                "Root cause analysis and remediation".to_string(),
                "Approval required for re-escalation".to_string(),
            ],
        }
    }

    /// Get monitoring conditions for a GAL level
    fn get_monitoring_conditions(&self, gal: GalLevel) -> Vec<String> {
        match gal {
            GalLevel::Manual => vec![
                "Human approval required for all operations".to_string(),
                "Basic compliance monitoring".to_string(),
            ],
            GalLevel::Assisted => vec![
                "Enhanced logging and audit trails".to_string(),
                "Regular compliance reviews".to_string(),
            ],
            GalLevel::Supervised => vec![
                "Automated compliance monitoring".to_string(),
                "Real-time performance tracking".to_string(),
                "Weekly governance reviews".to_string(),
            ],
            GalLevel::Conditional => vec![
                "Continuous constitutional monitoring".to_string(),
                "Automated violation detection".to_string(),
                "Daily compliance reporting".to_string(),
            ],
            GalLevel::HighAutonomy => vec![
                "Advanced safety monitoring systems".to_string(),
                "Real-time risk assessment".to_string(),
                "Comprehensive audit trails".to_string(),
            ],
            GalLevel::FullAutonomy => vec![
                "Maximum security monitoring".to_string(),
                "Continuous board-level oversight".to_string(),
                "Emergency protocol readiness".to_string(),
            ],
        }
    }

    /// Estimate timeline for GAL change
    fn estimate_timeline(&self, action: &GalAction, current: GalLevel, target: GalLevel) -> Option<Duration> {
        match action {
            GalAction::Maintain => None,
            GalAction::EmergencyDeEscalate => Some(Duration::minutes(5)), // Immediate
            GalAction::DeEscalate => Some(Duration::hours(1)), // Quick response
            GalAction::Escalate => {
                // Based on stability requirements
                self.stability_requirements.get(&target).copied()
            }
        }
    }

    /// Apply a GAL change (with proper authorization)
    pub async fn apply_gal_change(&self, recommendation: &GalRecommendation, operator: Option<String>) -> Result<()> {
        let mut current_gal = self.current_gal.write().await;
        let mut history = self.gal_history.write().await;

        // Create change record
        let change_record = GalChangeRecord {
            timestamp: Utc::now(),
            from_gal: *current_gal,
            to_gal: recommendation.recommended_gal,
            trigger: if operator.is_some() { GalChangeTrigger::Manual } else { GalChangeTrigger::Automated },
            compliance_score: 0.0, // Would be filled from context
            risk_level: RiskLevel::Low, // Would be calculated
            justification: recommendation.reasoning.clone(),
            operator,
        };

        // Apply the change
        *current_gal = recommendation.recommended_gal;
        history.push(change_record);

        tracing::info!(
            "GAL level changed: {} -> {} ({})",
            recommendation.current_gal.description(),
            recommendation.recommended_gal.description(),
            recommendation.reasoning
        );

        Ok(())
    }

    /// Get GAL change history
    pub async fn get_gal_history(&self) -> Vec<GalChangeRecord> {
        self.gal_history.read().await.clone()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_gal_manager_creation() {
        let manager = GalManager::new(GalLevel::Assisted);
        assert_eq!(manager.get_current_gal().await, GalLevel::Assisted);
    }

    #[tokio::test]
    async fn test_risk_assessment() {
        let manager = GalManager::new(GalLevel::Supervised);
        
        let context = GalAssessmentContext {
            current_gal: GalLevel::Supervised,
            compliance_score: 9.2,
            compliance_history: vec![],
            directive_results: vec![],
            violation_count: 0,
            critical_violations: 0,
            system_metrics: HashMap::new(),
            last_gal_change: None,
            human_overrides: vec![],
        };

        let recommendation = manager.assess_gal_level(&context).await.unwrap();
        assert!(recommendation.confidence > 0.0);
    }
}
