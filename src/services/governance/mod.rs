pub mod gal;
pub mod gal_manager;
pub mod directives;
pub mod audit;
pub mod validation_engine;

use tonic::{Request, Response, Status};
use std::path::Path;
use crate::ckodex::{
    governance_service_server::GovernanceService,
    ConstitutionalAuditRequest, ConstitutionalAuditResponse,
    GalEscalationRequest, GalEscalationResponse,
    ComplianceStatusRequest, ComplianceStatusResponse,
    AuditReportRequest, AuditReportResponse,
};

use gal::{GalLevel, GalValidator};
use directives::{DirectiveValidatorEngine, create_validation_context};
use audit::AuditEngine;

#[derive(Default)]
pub struct GovernanceServiceImpl {
    gal_validator: GalValidator,
    directive_validator: DirectiveValidatorEngine,
    audit_engine: AuditEngine,
}

#[tonic::async_trait]
impl GovernanceService for GovernanceServiceImpl {
    async fn constitutional_audit(
        &self,
        request: Request<ConstitutionalAuditRequest>,
    ) -> Result<Response<ConstitutionalAuditResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Performing constitutional audit on: {}", req.project_path);
        
        // Validate project path exists
        let project_path = Path::new(&req.project_path);
        if !project_path.exists() {
            return Err(Status::not_found("Project path does not exist"));
        }

        // Create validation context
        let mut validation_context = create_validation_context(
            req.project_path.clone(),
            1, // Default GAL level, could be determined from project config
        );

        // Add system metrics if available
        // In a real implementation, these would come from monitoring systems
        validation_context.system_metrics.insert("error_rate".to_string(), 2.5);
        validation_context.system_metrics.insert("uptime".to_string(), 99.2);
        validation_context.system_metrics.insert("cpu_usage".to_string(), 45.0);
        validation_context.system_metrics.insert("memory_usage".to_string(), 62.0);

        // Perform directive validation
        let directive_results = self.directive_validator
            .validate_all(&validation_context)
            .await
            .map_err(|e| Status::internal(format!("Directive validation failed: {}", e)))?;

        // Calculate overall compliance score
        let overall_score = self.directive_validator.calculate_overall_score(&directive_results);

        // Convert directive results to proto format
        let proto_directive_results: Vec<crate::ckodex::DirectiveCompliance> = directive_results
            .iter()
            .map(|result| crate::ckodex::DirectiveCompliance {
                directive_id: result.directive_id.clone(),
                directive_name: result.directive_name.clone(),
                compliance_score: result.compliance_score,
                issues: result.issues.clone(),
                recommendations: result.recommendations.clone(),
            })
            .collect();

        // Determine compliance status
        let compliance_status = if overall_score >= 9.0 {
            "COMPLIANT"
        } else if overall_score >= 7.0 {
            "PARTIALLY_COMPLIANT"
        } else if overall_score >= 5.0 {
            "NON_COMPLIANT"
        } else {
            "CRITICAL"
        };

        // Generate recommendations
        let mut all_recommendations = Vec::new();
        for result in &directive_results {
            all_recommendations.extend(result.recommendations.clone());
        }

        // Create audit response
        let audit_result = crate::ckodex::ConstitutionalAuditResponse {
            overall_compliance_score: overall_score,
            compliance_status: compliance_status.to_string(),
            directive_results: proto_directive_results,
            recommendations: all_recommendations,
            gal_recommendation: if overall_score >= 8.0 { 2 } else { 1 },
            audit_timestamp: chrono::Utc::now().timestamp(),
        };

        Ok(Response::new(audit_result))
    }

    async fn request_gal_escalation(
        &self,
        request: Request<GalEscalationRequest>,
    ) -> Result<Response<GalEscalationResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!(
            "GAL escalation request: {} -> {} ({})",
            req.current_gal,
            req.target_gal,
            req.justification
        );
        
        // Validate GAL levels
        let current_gal = GalLevel::from_i32(req.current_gal)
            .ok_or_else(|| Status::invalid_argument("Invalid current GAL level"))?;
        
        let target_gal = GalLevel::from_i32(req.target_gal)
            .ok_or_else(|| Status::invalid_argument("Invalid target GAL level"))?;

        // Process escalation request
        let escalation_result = self.gal_validator
            .evaluate_escalation(current_gal, target_gal, &req.justification, &req.project_context)
            .await
            .map_err(|e| Status::internal(format!("Escalation evaluation failed: {}", e)))?;

        Ok(Response::new(escalation_result))
    }

    async fn get_compliance_status(
        &self,
        request: Request<ComplianceStatusRequest>,
    ) -> Result<Response<ComplianceStatusResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Getting compliance status for: {}", req.project_path);
        
        let project_path = Path::new(&req.project_path);
        if !project_path.exists() {
            return Err(Status::not_found("Project path does not exist"));
        }

        // Get compliance status for all frameworks
        let frameworks = self.audit_engine
            .get_compliance_frameworks(project_path)
            .await
            .map_err(|e| Status::internal(format!("Failed to get compliance status: {}", e)))?;

        Ok(Response::new(ComplianceStatusResponse { frameworks }))
    }

    async fn generate_audit_report(
        &self,
        request: Request<AuditReportRequest>,
    ) -> Result<Response<AuditReportResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Generating audit report for: {}", req.project_path);
        
        let project_path = Path::new(&req.project_path);
        if !project_path.exists() {
            return Err(Status::not_found("Project path does not exist"));
        }
        
        // Generate report
        let report_data = self.audit_engine
            .generate_report(project_path, &req.format, req.include_details)
            .await
            .map_err(|e| Status::internal(format!("Failed to generate report: {}", e)))?;

        Ok(Response::new(AuditReportResponse {
            report_data,
            format: req.format,
        }))
    }
}