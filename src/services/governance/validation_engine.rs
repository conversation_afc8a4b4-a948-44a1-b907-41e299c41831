use anyhow::Result;
use tokio::sync::{mpsc, RwLock};
use tokio::time::{interval, Duration};
use std::collections::HashMap;
use std::sync::Arc;
use chrono::Utc;

use super::directives::{
    DirectiveValidatorEngine, DirectiveValidationResult, ValidationContext, 
    create_validation_context, ComplianceStatus
};

/// Real-time validation engine that continuously monitors constitutional compliance
pub struct RealTimeValidationEngine {
    directive_validator: DirectiveValidatorEngine,
    validation_results: Arc<RwLock<HashMap<String, DirectiveValidationResult>>>,
    violation_sender: Option<mpsc::UnboundedSender<ViolationAlert>>,
    is_running: Arc<RwLock<bool>>,
}

/// Violation alert structure
#[derive(Debug, Clone)]
pub struct ViolationAlert {
    pub directive_id: String,
    pub directive_name: String,
    pub severity: AlertSeverity,
    pub message: String,
    pub timestamp: chrono::DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// Alert severity levels
#[derive(Debug, Clone, PartialEq)]
pub enum AlertSeverity {
    Info,
    Warning,
    Error,
    Critical,
}

impl AlertSeverity {
    pub fn from_compliance_status(status: &ComplianceStatus) -> Self {
        match status {
            ComplianceStatus::Compliant => AlertSeverity::Info,
            ComplianceStatus::Warning => AlertSeverity::Warning,
            ComplianceStatus::Violation => AlertSeverity::Error,
            ComplianceStatus::Critical => AlertSeverity::Critical,
        }
    }

    pub fn as_str(&self) -> &'static str {
        match self {
            AlertSeverity::Info => "info",
            AlertSeverity::Warning => "warning",
            AlertSeverity::Error => "error",
            AlertSeverity::Critical => "critical",
        }
    }
}

impl RealTimeValidationEngine {
    pub fn new() -> Self {
        Self {
            directive_validator: DirectiveValidatorEngine::new(),
            validation_results: Arc::new(RwLock::new(HashMap::new())),
            violation_sender: None,
            is_running: Arc::new(RwLock::new(false)),
        }
    }

    /// Start the real-time validation engine
    pub async fn start(&mut self, project_path: String, gal_level: i32) -> Result<mpsc::UnboundedReceiver<ViolationAlert>> {
        let (tx, rx) = mpsc::unbounded_channel();
        self.violation_sender = Some(tx.clone());

        let directive_validator = DirectiveValidatorEngine::new();
        let validation_results = Arc::clone(&self.validation_results);
        let is_running = Arc::clone(&self.is_running);

        // Mark as running
        *is_running.write().await = true;

        // Spawn the validation loop
        tokio::spawn(async move {
            let mut validation_interval = interval(Duration::from_secs(30)); // Validate every 30 seconds
            
            tracing::info!("Starting real-time constitutional validation engine");
            
            loop {
                validation_interval.tick().await;
                
                // Check if we should continue running
                if !*is_running.read().await {
                    tracing::info!("Stopping real-time validation engine");
                    break;
                }

                // Create validation context
                let mut context = create_validation_context(project_path.clone(), gal_level);
                
                // Add current system metrics (in real implementation, these would come from monitoring)
                context.system_metrics.insert("error_rate".to_string(), 2.1);
                context.system_metrics.insert("uptime".to_string(), 99.5);
                context.system_metrics.insert("cpu_usage".to_string(), 42.0);
                context.system_metrics.insert("memory_usage".to_string(), 58.0);
                
                // Add historical data from previous validations
                {
                    let results = validation_results.read().await;
                    context.historical_data = results.values().cloned().collect();
                }

                // Perform validation
                match directive_validator.validate_all(&context).await {
                    Ok(results) => {
                        let mut updated_results = validation_results.write().await;
                        
                        for result in results {
                            // Check for violations and send alerts
                            if result.status != ComplianceStatus::Compliant {
                                let alert = ViolationAlert {
                                    directive_id: result.directive_id.clone(),
                                    directive_name: result.directive_name.clone(),
                                    severity: AlertSeverity::from_compliance_status(&result.status),
                                    message: if result.issues.is_empty() {
                                        format!("Compliance issue detected in {}", result.directive_name)
                                    } else {
                                        result.issues.join("; ")
                                    },
                                    timestamp: Utc::now(),
                                    metadata: result.metadata.clone(),
                                };

                                if let Err(e) = tx.send(alert) {
                                    tracing::error!("Failed to send violation alert: {}", e);
                                }
                            }

                            // Update stored results
                            updated_results.insert(result.directive_id.clone(), result);
                        }
                    }
                    Err(e) => {
                        tracing::error!("Validation failed: {}", e);
                        
                        // Send critical alert for validation failure
                        let alert = ViolationAlert {
                            directive_id: "SYSTEM".to_string(),
                            directive_name: "System Validation".to_string(),
                            severity: AlertSeverity::Critical,
                            message: format!("Constitutional validation system failure: {}", e),
                            timestamp: Utc::now(),
                            metadata: HashMap::new(),
                        };

                        if let Err(e) = tx.send(alert) {
                            tracing::error!("Failed to send system alert: {}", e);
                        }
                    }
                }
            }
        });

        Ok(rx)
    }

    /// Stop the real-time validation engine
    pub async fn stop(&self) {
        *self.is_running.write().await = false;
        tracing::info!("Real-time validation engine stop requested");
    }

    /// Get current validation results
    pub async fn get_current_results(&self) -> HashMap<String, DirectiveValidationResult> {
        self.validation_results.read().await.clone()
    }

    /// Get overall compliance score
    pub async fn get_overall_compliance_score(&self) -> f32 {
        let results = self.validation_results.read().await;
        let result_vec: Vec<DirectiveValidationResult> = results.values().cloned().collect();
        self.directive_validator.calculate_overall_score(&result_vec)
    }

    /// Check if the engine is running
    pub async fn is_running(&self) -> bool {
        *self.is_running.read().await
    }

    /// Trigger manual validation
    pub async fn trigger_manual_validation(&self, project_path: String, gal_level: i32) -> Result<Vec<DirectiveValidationResult>> {
        let mut context = create_validation_context(project_path, gal_level);
        
        // Add current system metrics
        context.system_metrics.insert("error_rate".to_string(), 1.8);
        context.system_metrics.insert("uptime".to_string(), 99.7);
        context.system_metrics.insert("cpu_usage".to_string(), 38.0);
        context.system_metrics.insert("memory_usage".to_string(), 55.0);
        
        // Add historical data
        {
            let results = self.validation_results.read().await;
            context.historical_data = results.values().cloned().collect();
        }

        let results = self.directive_validator.validate_all(&context).await?;
        
        // Update stored results
        {
            let mut stored_results = self.validation_results.write().await;
            for result in &results {
                stored_results.insert(result.directive_id.clone(), result.clone());
            }
        }

        Ok(results)
    }
}

impl Default for RealTimeValidationEngine {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::TempDir;

    #[tokio::test]
    async fn test_validation_engine_creation() {
        let engine = RealTimeValidationEngine::new();
        assert!(!engine.is_running().await);
    }

    #[tokio::test]
    async fn test_manual_validation() {
        let engine = RealTimeValidationEngine::new();
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path().to_string_lossy().to_string();
        
        let results = engine.trigger_manual_validation(project_path, 1).await.unwrap();
        assert!(!results.is_empty());
        assert_eq!(results.len(), 10); // Should validate all 10 directives
    }

    #[tokio::test]
    async fn test_overall_compliance_score() {
        let engine = RealTimeValidationEngine::new();
        let temp_dir = TempDir::new().unwrap();
        let project_path = temp_dir.path().to_string_lossy().to_string();
        
        // Trigger validation to populate results
        let _results = engine.trigger_manual_validation(project_path, 1).await.unwrap();
        
        let score = engine.get_overall_compliance_score().await;
        assert!(score >= 0.0 && score <= 10.0);
    }
}
