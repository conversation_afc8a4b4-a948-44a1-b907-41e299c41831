use tonic::{Request, Response, Status};
use tokio_stream::wrappers::ReceiverStream;
use tokio::sync::mpsc;
use std::time::{Duration, SystemTime, UNIX_EPOCH};
use anyhow::Result;

use crate::ckodex::{
    monitoring_service_server::MonitoringService,
    MetricsRequest, MetricsResponse, ThresholdRequest, ThresholdResponse,
    AlertRequest, AlertResponse, TrendsRequest, TrendsResponse,
    AssessmentRequest, AssessmentResponse, HealthRequest, HealthResponse,
    DirectiveScore, SystemHealthMetrics, PerformanceMetrics,
    ThresholdConfig, TrendData, DataPoint,
};

use crate::database::models::{
    RealTimeMetrics, ComplianceThreshold, AlertConfiguration, ActiveAlert, MetricHistory
};

use crate::services::governance::validation_engine::{RealTimeValidationEngine, ViolationAlert};

/// Real-time Constitutional Monitoring Service Implementation
pub struct MonitoringServiceImpl {
    // Database connection pool would be injected here
    // pub db: DatabaseConnection,
    validation_engine: RealTimeValidationEngine,
}

impl Default for MonitoringServiceImpl {
    fn default() -> Self {
        Self::new()
    }
}

impl MonitoringServiceImpl {
    pub fn new() -> Self {
        Self {
            validation_engine: RealTimeValidationEngine::new(),
        }
    }

    /// Calculate overall compliance score based on directive scores
    fn calculate_overall_compliance(&self, directive_scores: &[DirectiveScore]) -> f32 {
        if directive_scores.is_empty() {
            return 0.0;
        }
        
        let total: f32 = directive_scores.iter().map(|d| d.score).sum();
        total / directive_scores.len() as f32
    }

    /// Get current Primal Directive scores from the validation engine
    async fn get_directive_scores(&self) -> Result<Vec<DirectiveScore>> {
        let validation_results = self.validation_engine.get_current_results().await;

        if validation_results.is_empty() {
            // If no results yet, trigger a manual validation
            let results = self.validation_engine
                .trigger_manual_validation(".".to_string(), 1)
                .await?;

            // Convert to DirectiveScore format
            Ok(results.into_iter().map(|result| DirectiveScore {
                directive_id: result.directive_id,
                directive_name: result.directive_name,
                score: result.compliance_score,
                status: result.status.as_str().to_string(),
                issues: result.issues,
            }).collect())
        } else {
            // Convert stored results to DirectiveScore format
            Ok(validation_results.into_values().map(|result| DirectiveScore {
                directive_id: result.directive_id,
                directive_name: result.directive_name,
                score: result.compliance_score,
                status: result.status.as_str().to_string(),
                issues: result.issues,
            }).collect())
        }
    }

    /// Get current system health metrics
    async fn get_system_health(&self) -> Result<SystemHealthMetrics> {
        // This would normally collect real system metrics
        Ok(SystemHealthMetrics {
            cpu_usage: 45.2,
            memory_usage: 62.8,
            disk_usage: 34.1,
            active_connections: 127,
            response_time_avg: 23.5,
            error_rate: 2,
        })
    }

    /// Get current performance metrics
    async fn get_performance_metrics(&self) -> Result<PerformanceMetrics> {
        // This would normally collect real performance data
        Ok(PerformanceMetrics {
            requests_per_second: 1250,
            average_response_time: 23.5,
            concurrent_users: 89,
            throughput: 2.4,
        })
    }

    /// Generate a real-time metrics response
    async fn generate_metrics_response(&self, gal_level: i32) -> Result<MetricsResponse> {
        let directive_scores = self.get_directive_scores().await?;
        let overall_score = self.calculate_overall_compliance(&directive_scores);
        let health = self.get_system_health().await?;
        let performance = self.get_performance_metrics().await?;
        
        let timestamp = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as i64;

        Ok(MetricsResponse {
            timestamp,
            overall_compliance_score: overall_score,
            directive_scores,
            current_gal_level: gal_level,
            health: Some(health),
            active_violations: 1, // Would be calculated from actual violations
            performance: Some(performance),
        })
    }
}

#[tonic::async_trait]
impl MonitoringService for MonitoringServiceImpl {
    type GetRealTimeMetricsStream = ReceiverStream<Result<MetricsResponse, Status>>;
    type GetViolationAlertsStream = ReceiverStream<Result<AlertResponse, Status>>;

    async fn get_real_time_metrics(
        &self,
        request: Request<MetricsRequest>,
    ) -> Result<Response<Self::GetRealTimeMetricsStream>, Status> {
        let req = request.into_inner();
        let gal_level = req.gal_level;
        
        tracing::info!("Starting real-time metrics stream for GAL level: {}", gal_level);
        
        let (tx, rx) = mpsc::channel(128);
        
        // Spawn a task to send periodic metrics updates
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(5));
            
            loop {
                interval.tick().await;
                
                // Generate metrics response (in real implementation, this would be more sophisticated)
                let metrics_response = MetricsResponse {
                    timestamp: SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs() as i64,
                    overall_compliance_score: 9.1,
                    directive_scores: vec![
                        DirectiveScore {
                            directive_id: "PD-001".to_string(),
                            directive_name: "Human Oversight".to_string(),
                            score: 9.2,
                            status: "compliant".to_string(),
                            issues: vec![],
                        },
                    ],
                    current_gal_level: gal_level,
                    health: Some(SystemHealthMetrics {
                        cpu_usage: 45.0,
                        memory_usage: 60.0,
                        disk_usage: 30.0,
                        active_connections: 100,
                        response_time_avg: 25.0,
                        error_rate: 1,
                    }),
                    active_violations: 0,
                    performance: Some(PerformanceMetrics {
                        requests_per_second: 1000,
                        average_response_time: 25.0,
                        concurrent_users: 50,
                        throughput: 2.0,
                    }),
                };
                
                if tx.send(Ok(metrics_response)).await.is_err() {
                    break; // Client disconnected
                }
            }
        });
        
        Ok(Response::new(ReceiverStream::new(rx)))
    }

    async fn set_compliance_thresholds(
        &self,
        request: Request<ThresholdRequest>,
    ) -> Result<Response<ThresholdResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Setting compliance thresholds for GAL level: {}", req.gal_level);
        
        // In a real implementation, this would save to database
        let response = ThresholdResponse {
            success: true,
            message: format!("Successfully set {} thresholds for GAL level {}", 
                           req.thresholds.len(), req.gal_level),
            applied_thresholds: req.thresholds,
        };
        
        Ok(Response::new(response))
    }

    async fn get_violation_alerts(
        &self,
        request: Request<AlertRequest>,
    ) -> Result<Response<Self::GetViolationAlertsStream>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Starting violation alerts stream");
        
        let (tx, rx) = mpsc::channel(128);
        
        // Spawn a task to send periodic alert updates
        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));
            
            loop {
                interval.tick().await;
                
                // Generate sample alert (in real implementation, this would come from actual monitoring)
                let alert = AlertResponse {
                    alert_id: uuid::Uuid::new_v4().to_string(),
                    severity: "warning".to_string(),
                    directive_id: "PD-004".to_string(),
                    message: "Minor bias detected in decision patterns".to_string(),
                    timestamp: SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs() as i64,
                    resolved: false,
                    resolution_notes: "".to_string(),
                    metadata: std::collections::HashMap::new(),
                };
                
                if tx.send(Ok(alert)).await.is_err() {
                    break; // Client disconnected
                }
            }
        });
        
        Ok(Response::new(ReceiverStream::new(rx)))
    }

    async fn get_compliance_trends(
        &self,
        request: Request<TrendsRequest>,
    ) -> Result<Response<TrendsResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Getting compliance trends from {} to {}", req.start_time, req.end_time);
        
        // Generate sample trend data
        let trends = vec![
            TrendData {
                metric_name: "overall_compliance_score".to_string(),
                data_points: vec![
                    DataPoint { timestamp: req.start_time, value: 8.8 },
                    DataPoint { timestamp: req.start_time + 3600, value: 9.0 },
                    DataPoint { timestamp: req.start_time + 7200, value: 9.1 },
                    DataPoint { timestamp: req.end_time, value: 9.2 },
                ],
            },
        ];
        
        Ok(Response::new(TrendsResponse { trends }))
    }

    async fn trigger_assessment(
        &self,
        request: Request<AssessmentRequest>,
    ) -> Result<Response<AssessmentResponse>, Status> {
        let req = request.into_inner();
        
        tracing::info!("Triggering manual assessment for: {}", req.target_path);
        
        let directive_scores = self.get_directive_scores().await
            .map_err(|e| Status::internal(format!("Failed to get directive scores: {}", e)))?;
        
        let overall_score = self.calculate_overall_compliance(&directive_scores);
        
        let response = AssessmentResponse {
            assessment_id: uuid::Uuid::new_v4().to_string(),
            compliance_score: overall_score,
            directive_results: directive_scores,
            recommendations: vec![
                "Consider implementing additional bias detection mechanisms".to_string(),
                "Enhance transparency reporting for decision processes".to_string(),
            ],
            assessment_timestamp: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap()
                .as_secs() as i64,
        };
        
        Ok(Response::new(response))
    }

    async fn get_system_health(
        &self,
        _request: Request<HealthRequest>,
    ) -> Result<Response<HealthResponse>, Status> {
        tracing::info!("Getting system health status");
        
        let health_metrics = self.get_system_health().await
            .map_err(|e| Status::internal(format!("Failed to get health metrics: {}", e)))?;
        
        let status = if health_metrics.cpu_usage < 80.0 && health_metrics.memory_usage < 85.0 {
            "healthy"
        } else if health_metrics.cpu_usage < 90.0 && health_metrics.memory_usage < 95.0 {
            "degraded"
        } else {
            "unhealthy"
        };
        
        let response = HealthResponse {
            status: status.to_string(),
            metrics: Some(health_metrics),
            issues: if status == "healthy" { 
                vec![] 
            } else { 
                vec!["High resource utilization detected".to_string()] 
            },
            uptime_seconds: 86400, // Would be calculated from actual uptime
        };
        
        Ok(Response::new(response))
    }
}
